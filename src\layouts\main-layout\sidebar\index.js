import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import DrawerItems from './DrawerItems';
const Sidebar = ({ mobileOpen, setMobileOpen, setIsClosing }) => {
    const handleDrawerClose = () => {
        setIsClosing(true);
        setMobileOpen(false);
    };
    const handleDrawerTransitionEnd = () => {
        setIsClosing(false);
    };
    return (_jsxs(Box, { component: "nav", width: { lg: 300 }, flexShrink: { lg: 0 }, display: { xs: 'none', lg: 'block' }, children: [_jsx(Drawer, { variant: "temporary", open: mobileOpen, onTransitionEnd: handleDrawerTransitionEnd, onClose: handleDrawerClose, ModalProps: { keepMounted: true }, sx: { display: { xs: 'block', lg: 'none' } }, children: _jsx(DrawerItems, {}) }), _jsx(Drawer, { variant: "permanent", sx: { display: { xs: 'none', lg: 'block' } }, open: true, children: _jsx(DrawerItems, {}) })] }));
};
export default Sidebar;
