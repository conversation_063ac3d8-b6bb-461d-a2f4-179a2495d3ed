import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery, useQueryClient } from 'react-query';
import { deleteQuestion } from 'pages/helpQuestions/api/deleteQuestion.ts';

type QuestionPros = {
    categoryId: string;
    subcategoryId: string;
    questionId: string;
}

export default function useDeleteQuestion(props: QuestionPros) {
  const queryClient = useQueryClient();
  const { refetch: questionDelete, loading: questionDelLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['delete-question', { ...props }],
      queryFn: () => deleteQuestion(props),
      onError: showError,
      onSuccess: () => queryClient.invalidateQueries(['sub-category-questions']).finally(),
      enabled: false,
    }),
    transform: (data) => data,
  });
  return { questionDelete, questionDelLoading };
}