import { UserDto } from 'pages/authentication/types/user.ts';

export interface MessageThreadDto {
  _id: string;
  offer: null;
  client: UserDto;
  seller: UserDto;
  unreadClient: number;
  unreadSeller: number;
  archived: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
  lastMessage: string;
}

// Messages

export interface MessageDto {
  _id: string;
  conversation: string;
  sender: Sender;
  content: string;
  read: boolean;
  attachments: string[];
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface Sender {
  _id: string;
  name: string;
  role: 'seller' | 'buyer' | string;
  avatar: string;
}