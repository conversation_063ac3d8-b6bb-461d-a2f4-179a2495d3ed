import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery, useQueryClient } from 'react-query';
import { postLevel } from 'pages/levels/api/postLevel.ts';

export default function useResetLevels() {
  const queryClient = useQueryClient();

  const { loading: resetLoading, refetch: resetRefetch } = useQueryFix({
    query: useQuery({
      queryKey: ['post-level'],
      queryFn: () => postLevel(),
      onError: showError,
      enabled: false,
      onSuccess: () => queryClient.invalidateQueries(['levels']),
    }),
    transform: (response) => response.data,
  });
  return { resetRefetch, resetLoading };
}
