import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import getCategoryDetails from 'pages/category/api/getCategoryDetails.ts';
import { useParams } from 'react-router-dom';

export default function useCategoryDetails() {
  const { id = '' } = useParams();
  const { data: categoryDetails, loading: categoryDetailsLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['sub-categories-details', id],
      queryFn: () => getCategoryDetails({ id }),
      onError: showError,
    }),
    transform: (data) => data.data,
  });
  return { categoryDetails, categoryDetailsLoading };
}
