import { postRequest } from 'vbrae-utils';

interface BannerPropsDto {
  link: string;
  order: number;
  location: string;
    logoImage?: string;
    images: {
        desktop?: string,
        phone?: string,
        tablet?: string
    }
}

export async function postBannerForm(props: BannerPropsDto): Promise<{message: string} | undefined> {
    const r = await postRequest({
        url: 'banners',
        data : props,
        useAuth: true
    });
    return r.response
}