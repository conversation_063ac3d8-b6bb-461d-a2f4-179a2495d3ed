import * as Yup from 'yup';
import { useFormik } from 'formik';
import { useEffect, useState } from 'react';
import { useMutation, useQueryClient } from 'react-query';
import { showError, showSuccess } from 'vbrae-utils';
import { patchQuestion } from 'pages/helpQuestions/api/patchQuestion.ts';
import { postQuestion } from 'pages/helpQuestions/api/postQuestion.ts';
import { useParams } from 'react-router-dom';

type QuestionFormPropsDto = {
  existingQuestion?: {
    _id: string;
    title: string;
    content: string;
    isActive: boolean;
  };
  handleClose: () => void;
};

export type ErrorType = {
  message: string;
};

export const editSchema = Yup.object().shape({
    isActive: Yup.boolean(),
    title: Yup.string(),
    content: Yup.string(),
});

export default function useQuestionsForm({existingQuestion, handleClose}: QuestionFormPropsDto) {
    
    const { id = "", subCategoryId = "" } = useParams();
    
    const [initialValues, setInitialState] = useState({
        isActive: false,
        title: "",
        content: "",
    });

    useEffect(() => {
        if (!existingQuestion?._id) return;

        setInitialState({
            isActive: existingQuestion.isActive,
            title: existingQuestion.title,
            content: existingQuestion.content,
        });
    }, [existingQuestion]);

    const { mutateAsync : updateASync } = useMutation(patchQuestion, {
        onError: (error: ErrorType) => showError(error),
    });

    const { mutateAsync } = useMutation(postQuestion, {
        onError: (error: ErrorType) => showError(error),
    });

    const queryClient = useQueryClient();

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: editSchema,
        onSubmit: async (values, { setSubmitting }) => {
            setSubmitting(true)
            let response;
            if(!existingQuestion?._id) response = await mutateAsync({ ...values, categoryId : id, subcategoryId: subCategoryId });
            else response = await updateASync({ ...values, categoryId : id, subcategoryId: subCategoryId, questionId: existingQuestion._id });
            setSubmitting(false);
            if(response){
                queryClient.invalidateQueries("sub-category-questions").finally(()=> showSuccess("Successfully updated question"))
                handleClose();
            }
        },
    });

    return { formik };
}
