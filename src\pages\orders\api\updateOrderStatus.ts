import { patchRequest } from 'vbrae-utils';

interface UpdateOrderStatusPayload {
  status: string;
}

interface UpdateOrderStatusResponse {
  status: string;
  message: string;
}

export async function updateOrderStatus({
  orderId,
  status
}: {
  orderId: string;
  status: string;
}): Promise<UpdateOrderStatusResponse> {
  return await patchRequest({
    url: `order/update-status/${orderId}`,
    data: { status },
    useAuth: true,
  });
}
