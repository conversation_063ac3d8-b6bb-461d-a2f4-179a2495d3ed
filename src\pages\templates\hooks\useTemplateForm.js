import { useMutation } from 'react-query';
import * as Yup from 'yup';
import { useFormik } from 'formik';
import { showError, showSuccess } from 'vbrae-utils';
import { useEffect, useState } from 'react';
import { postTemplate } from 'pages/templates/api/postTemplate.ts';
import { uploadUrl } from 'pages/category/api/uploadUrl.ts';
import { updateTemplate } from 'pages/templates/api/updateTemplate.ts';
const templateSchema = Yup.object().shape({
    coverImage: Yup.mixed().nullable(),
    templateName: Yup.string()
        .trim()
        .min(3, 'Minimum 3 characters')
        .max(50, 'Maximum 50 characters')
        .required('Template Name is required'),
    slug: Yup.string()
        .trim()
        .min(3, 'Minimum 3 characters')
        .max(50, 'Maximum 50 characters')
        .required('Slug is required'),
    price: Yup.number()
        .required('Price is required'),
    listingType: Yup.object().shape({
        _id: Yup.string().min(4, 'Listing ID is required').required(),
        name: Yup.string(),
    }),
    category: Yup.object().shape({
        _id: Yup.string().min(4, 'Category ID is required').required(),
        name: Yup.string(),
    }),
    region: Yup.object().shape({
        _id: Yup.string().min(4, 'Region ID is required').required(),
        name: Yup.string(),
    }),
    genres: Yup.string()
        .required('At least one genre is required'),
    releaseDate: Yup.date()
        .required('Release Date is required'),
    preOrder: Yup.object().shape({
        _id: Yup.string().min(4, 'Order ID is required').required(),
        name: Yup.string(),
    }),
    dlc: Yup.string()
        .min(3, 'Minimum 3 characters')
        .max(100, 'Maximum 100 characters')
        .notRequired(),
    specificCountrySellingOption: Yup.object().shape({
        _id: Yup.string().min(4, 'Region ID is required').required(),
        name: Yup.string(),
    }),
    languages: Yup.array().required('Languages are required'),
    videos: Yup.array()
        .of(Yup.string().url('Must be a valid URL'))
        .required('At least one video URL is required'),
    images: Yup.mixed().nullable(),
    details: Yup.object().shape({
        title: Yup.string()
            .trim()
            .min(3, 'Minimum 3 characters')
            .max(100, 'Maximum 100 characters')
            .required('Details Title is required'),
        description: Yup.string()
            .min(3, 'Minimum 3 characters')
            .max(500, 'Maximum 500 characters')
            .required('Details Description is required'),
        seo: Yup.object().shape({
            metaTitle: Yup.string()
                .min(3, 'Minimum 3 characters')
                .max(60, 'Maximum 60 characters')
                .required('SEO Meta Title is required'),
            metaDescription: Yup.string()
                .min(3, 'Minimum 3 characters')
                .max(160, 'Maximum 160 characters')
                .required('SEO Meta Description is required'),
            metaKeywords: Yup.string()
                .min(3, 'Minimum 3 characters')
                .max(200, 'Maximum 200 characters')
                .required('SEO Meta Keywords are required'),
        }),
    }),
});
;
export default function useTemplateForm({ templateDetails }) {
    const [initialValues, setInitialValues] = useState({
        coverImage: null,
        templateName: '',
        price: 1,
        slug: '',
        listingType: {
            _id: '0', name: "--Choose--"
        },
        category: {
            _id: '0', name: "--Choose--"
        },
        region: {
            _id: '0', name: "--Choose--"
        },
        genres: [],
        releaseDate: '',
        preOrder: {
            _id: '0', name: "--Choose--"
        },
        dlc: '',
        specificCountrySellingOption: {
            _id: '0', name: "--Choose--"
        },
        languages: [],
        videos: [],
        images: [],
        details: {
            title: '',
            description: '',
            seo: {
                metaTitle: '',
                metaDescription: '',
                metaKeywords: '',
            },
        },
    });
    useEffect(() => {
        if (!templateDetails)
            return;
        setInitialValues({
            price: templateDetails.price,
            coverImage: templateDetails.coverImage[0],
            templateName: templateDetails.templateName,
            slug: templateDetails.slug,
            listingType: { _id: templateDetails.listingType, name: templateDetails.listingType ?? '--Choose--' },
            category: { _id: templateDetails.category?._id ?? "", name: templateDetails.category?.categoryName.en ?? "None" },
            region: { _id: templateDetails.region, name: templateDetails.region ?? "--Choose--" },
            genres: templateDetails.genres,
            releaseDate: templateDetails.releaseDate.split('T')[0],
            preOrder: { _id: templateDetails.preOrder, name: '' },
            dlc: templateDetails.dlc,
            specificCountrySellingOption: { _id: templateDetails.specificCountrySellingOption, name: templateDetails.specificCountrySellingOption ? '' : '--Choose--' },
            languages: templateDetails.languages,
            videos: templateDetails.videos,
            images: templateDetails.images,
            details: {
                title: templateDetails.details?.title,
                description: templateDetails.details?.description,
                seo: {
                    metaTitle: templateDetails.details?.seo?.metaTitle,
                    metaDescription: templateDetails.details?.seo?.metaDescription,
                    metaKeywords: templateDetails.details?.seo?.metaKeywords,
                },
            },
        });
    }, [templateDetails]);
    const { mutateAsync: updateAsync } = useMutation(updateTemplate, {
        onError: (error) => showError(error),
    });
    const { mutateAsync } = useMutation(postTemplate, {
        onError: (error) => showError(error),
    });
    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: templateSchema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);
            let response;
            const imageUrls = [];
            if (Array.isArray(values.images) && values.images.length > 0) {
                for (const image of values.images) {
                    if (image instanceof File) {
                        const fileType = image.name.split('.').pop()?.toLowerCase() || 'unknown';
                        const resignedResponse = await uploadUrl({ name: image.name, fileType });
                        const { url: resignedUrl, path: filePath } = resignedResponse;
                        await fetch(resignedUrl, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': image.type,
                                'x-amz-acl': 'public-read'
                            },
                            body: image,
                        });
                        imageUrls.push(filePath);
                    }
                }
            }
            let coverImage = null;
            if (values.coverImage instanceof File) {
                const fileType = values.coverImage.name.split('.').pop()?.toLowerCase() || "unknown";
                const resignedResponse = await uploadUrl({ name: values.coverImage.name, fileType });
                const { url: resignedUrl, path: filePath } = resignedResponse;
                await fetch(resignedUrl, {
                    method: "PUT",
                    headers: { "Content-Type": values.coverImage.type, "x-amz-acl": "public-read" },
                    body: values.coverImage,
                });
                coverImage = filePath;
            }
            if (!templateDetails)
                response = await mutateAsync({
                    ...values,
                    listingType: values.listingType._id,
                    category: values.category._id,
                    region: values.region._id,
                    preOrder: Boolean(values.preOrder._id),
                    specificCountrySellingOption: Boolean(values.specificCountrySellingOption),
                    coverImage: coverImage,
                    images: imageUrls
                });
            if (templateDetails)
                response = await updateAsync({
                    ...values,
                    _id: templateDetails._id,
                    listingType: values.listingType._id,
                    category: values.category._id,
                    region: values.region._id,
                    preOrder: Boolean(values.preOrder._id),
                    specificCountrySellingOption: Boolean(values.specificCountrySellingOption),
                    coverImage: coverImage,
                    images: imageUrls
                });
            setSubmitting(false);
            resetForm();
            if (response) {
                showSuccess(response.message);
            }
        },
    });
    return { formik };
}
