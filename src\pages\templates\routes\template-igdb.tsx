import Paper from '@mui/material/Paper';
import { InputBase } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import IconButton from '@mui/material/IconButton';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { fontFamily } from 'theme/typography.ts';

export default function TemplateIgdb() {
  // const [searchValue, setSearchValue] = useState('');
  // const columns = useMemo<ColumnDef<unknow>[]>(
  //     () => [
  //         {
  //             header: '#',
  //             accessorFn: (row, index) => `${index + 1}`,
  //             id: '_index',
  //             cell: (info) => info.getValue(),
  //             size: 100,
  //         },
  //         {
  //             header: 'Username',
  //             accessorFn: (row) => row.title,
  //             id: 'title',
  //             size: 200,
  //             cell: (info) => info.getValue(),
  //         },
  //         {
  //             header: 'Title',
  //             accessorFn: (row) => row.title,
  //             size: 250,
  //             id: 'title',
  //             cell: (info) => info.getValue(),
  //         },
  //         {
  //             header: 'Regional Limit',
  //             accessorFn: (row) => row.regional_limit,
  //             size: 250,
  //             id: 'regional-limit',
  //             cell: (info) => info.getValue(),
  //         },
  //         {
  //             header: 'Platform',
  //             accessorFn: (row) => row.platform,
  //             size: 250,
  //             id: 'platform',
  //             cell: (info) => info.getValue(),
  //         },
  //         {
  //             header: 'Language',
  //             accessorFn: (row) => row.language,
  //             size: 250,
  //             id: 'language',
  //             cell: (info) => info.getValue(),
  //         },
  //         {
  //             header: 'Date',
  //             accessorFn: (row) => row.created_at,
  //             size: 250,
  //             id: 'created_at',
  //             cell: (info) => info.getValue(),
  //         },
  //         {
  //             header: 'Status',
  //             accessorFn: (row) => row.status,
  //             size: 250,
  //             id: 'status',
  //             cell: (info) => info.getValue(),
  //         },
  //         {
  //             header: 'Action',
  //             accessorFn: (row) => {},
  //             size: 5,
  //             id: 'action',
  //             cell: (info) => {
  //                 <span>Button</span>
  //             },
  //         },
  //     ],
  //     []
  // );

  return (
    <Stack direction="column" spacing={2}>
      <Typography
        variant="h5"
        fontWeight={600}
        letterSpacing={1}
        fontFamily={fontFamily.workSans}
        display={{ xs: 'none', lg: 'block' }}
      >
        Select Game
      </Typography>
      <Paper
        component="form"
        sx={{
          p: '2px 4px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: 400,
          marginLeft: 'auto',
        }}
      >
        <InputBase
          sx={{ ml: 1, flex: 1, border: 'none' }}
          placeholder="Search here"
          inputProps={{ 'aria-label': 'search google maps' }}
        />
        <IconButton type="button" sx={{ p: '10px' }} aria-label="search">
          <SearchIcon />
        </IconButton>
      </Paper>

      {/*<TableComponent*/}
      {/*    columns={columns}*/}
      {/*    data={[]}*/}
      {/*    globalFilter={searchValue}*/}
      {/*    setGlobalFilter={setSearchValue}*/}
      {/*/>*/}
    </Stack>
  );
}