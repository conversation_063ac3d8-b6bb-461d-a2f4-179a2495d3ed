import * as Yup from 'yup';
import { useMutation } from "react-query";
import { useFormik } from "formik";
import { useNavigate } from "react-router-dom";
import { postLoginForm } from "pages/authentication/api/postLoginForm.ts";
import { setAccessToken, showError } from "vbrae-utils";
const userLoginSchema = Yup.object().shape({
    email: Yup.string().trim()
        .email('Wrong email format')
        .min(3, 'Minimum 3 characters')
        .max(50, 'Maximum 50 characters')
        .required('Email is required'),
    password: Yup.string()
        .min(3, 'Minimum 3 characters')
        .max(50, 'Maximum 50 characters')
        .required('Password is required'),
    rememberMe: Yup.boolean(),
});
export default function useLoginForm() {
    const navigate = useNavigate();
    const initialValues = ({
        email: '',
        password: '',
        rememberMe: true
    });
    const { mutateAsync } = useMutation(postLoginForm, {
        onError: (error) => showError(error),
    });
    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: userLoginSchema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);
            const response = await mutateAsync(values);
            setSubmitting(false);
            resetForm();
            if (response) {
                if (response.user.role !== 'admin') {
                    showError({ message: 'Unauthorized for this resource' });
                    return;
                }
                setAccessToken(response.token, values.rememberMe);
                navigate('/');
            }
        },
    });
    return { formik };
}
