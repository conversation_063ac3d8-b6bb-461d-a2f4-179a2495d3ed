import * as Yup from 'yup';
import { useMutation } from 'react-query';
import { useFormik } from 'formik';
import {showError, showSuccess} from 'vbrae-utils';
import { updateProduct } from 'pages/products/api/updateProduct.ts';
import {SelectedValue} from "components/select-menu.tsx";
import {useEffect, useState} from "react";
import {Product} from "pages/products/types/product.ts";
import {uploadUrl} from "pages/category/api/uploadUrl.ts";

export type ErrorType = {
    message: string
}

const productSchema = Yup.object().shape({
    coverImage: Yup.mixed().required("Cover Image is required"),
    instantDelivery: Yup.string(),
    category:  Yup.object().shape({
        _id: Yup.string().min(2, 'Category ID is required').required("Category is required"),
        name: Yup.string(),
    }),
    region:Yup.object().shape({
        _id: Yup.string().nullable(),
        name: Yup.string(),
    }),
    genres: Yup.string(),
    slug: Yup.string().trim(),
    active:Yup.object().shape({
        _id: Yup.string().nullable(),
        name: Yup.string(),
    }),
    visibility:Yup.object().shape({
        _id: Yup.string().nullable(),
        name: Yup.string(),
    }),
});

interface InitialValuesDto {
    coverImage: null | string | File,
    slug: string;
    instantDelivery: string;
    category: SelectedValue;
    subcategory: SelectedValue;
    region: SelectedValue;
    active: SelectedValue;
    visibility: SelectedValue;
    genres: string;
};

export default function useProductForm({product} : {product?: Product}) {
    const [initialValues, setInitialValues] = useState<InitialValuesDto>({
        coverImage: null,
        slug: '',
        instantDelivery: 'true',
        category: {
            _id: null, name: "--Choose--"
        },
        subcategory: {
            _id: null, name: "--Choose--"
        },
        region: {
            _id: null, name: "--Choose--"
        },
        visibility: {
            _id: null, name: "--Choose--"
        },
        active: {
            _id: null, name: "--Choose--"
        },
        genres: '',
    });

    useEffect(() => {
        if(!product || !product.template) return;

        setInitialValues({
            coverImage: product.template.coverImage,
            slug: product.template.slug,
            instantDelivery: `${product.instantDelivery}`,
            category: {_id: product.category ?? "", name: product.category ?? "None"},
            subcategory: {_id: product.subcategory ?? "", name: product.subcategory ?? "None"},
            region: {_id: product.region, name: product.region ?? "--Choose--"},
            visibility: {_id: product.visibility, name: product.visibility ? "Visible" : "Hidden"},
            active: {_id: product.active, name: product.active ? "Active" : "Sold"},
            genres: product.template.genres,
        })
    }, [product]);

    const { mutateAsync } = useMutation(updateProduct, {
        onError: (error:ErrorType)=>showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: productSchema,
        onSubmit: async (values, { setSubmitting }) => {
            setSubmitting(true);

            let coverImage = product?.template?.coverImage;
            if(values.coverImage instanceof File) {
                const fileType = values.coverImage.name.split('.').pop()?.toLowerCase() || "unknown";
                const resignedResponse = await uploadUrl({ name: values.coverImage.name, fileType });
                const { url: resignedUrl, path: filePath } = resignedResponse;

                await fetch(resignedUrl, {
                    method: "PUT",
                    headers: { "Content-Type": values.coverImage.type, "x-amz-acl": "public-read" },
                    body: values.coverImage,
                });

                coverImage = filePath
            }

            const response = await mutateAsync({...values,
                _id: product?._id,
                coverImage,
                category: values.category._id,
                subcategory: values.subcategory._id,
                region: values.region._id,
                active: values.active._id,
                visibility: values.visibility._id,
                instantDelivery: JSON.parse(values.instantDelivery)});
            setSubmitting(false);
            if (response) {
                showSuccess("Product updated Successfully.");
            }
        },
    });

    return { formik };
}
