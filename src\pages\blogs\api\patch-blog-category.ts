import { patchRequest } from 'vbrae-utils';

type BlogCategoryProps = {
  _id: string;
  language: string;
  categoryName: string;
  description: string;
  keywords: string[];
  order: number;
};

export async function patchBlogCategory(props: BlogCategoryProps): Promise<undefined> {
    const r = await patchRequest<BlogCategoryProps>({
        url: `blog-category/${props._id}`,
        data : props,
        useAuth: true
    });
    return r.response
}