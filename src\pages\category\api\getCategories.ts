import {getRequest} from "vbrae-utils";
import {CategoryDto} from "pages/category/types/category.ts";
import {CategoryProps} from "pages/category/hooks/useCategories.ts";

export interface CategoriesResponse {
    data : CategoryDto[],
    status: string
}

export async function getCategories({order, sortBy} : CategoryProps): Promise<CategoriesResponse> {
    return await getRequest({
        url: `sub-catagory${sortBy ? `?sortBy=${sortBy}&order=${order}` : ""}`,
        useAuth: true,
    });
}
