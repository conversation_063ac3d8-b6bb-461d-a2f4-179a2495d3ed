import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery, useQueryClient } from 'react-query';
import { postTaxes } from 'pages/tax/api/postTaxes.ts';

export default function useResetTax() {

  const queryClient = useQueryClient();

  const { loading: resetLoading, refetch: resetRefetch } = useQueryFix({
    query: useQuery({
      queryKey: ['post-tax'],
      queryFn: () => postTaxes(),
      onError: showError,
      enabled: false,
      onSuccess: () => queryClient.invalidateQueries(['tax']),
    }),
    transform: (response) => response.data,
  });
  return { resetRefetch, resetLoading };
}
