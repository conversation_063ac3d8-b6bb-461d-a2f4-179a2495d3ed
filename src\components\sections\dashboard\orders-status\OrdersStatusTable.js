import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { ordersStatusData } from 'data/ordersStatusData';
import Stack from '@mui/material/Stack';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import Typography from '@mui/material/Typography';
import StatusChip from 'components/chips/StatusChip';
import IconifyIcon from 'components/base/IconifyIcon';
import DataGridFooter from 'components/common/DataGridFooter';
import { GridRowModes, DataGrid, GridActionsCellItem, GridRowEditStopReasons, useGridApiRef, } from '@mui/x-data-grid';
const OrdersStatusTable = ({ searchText }) => {
    const apiRef = useGridApiRef();
    const [rows, setRows] = useState(ordersStatusData);
    const [rowModesModel, setRowModesModel] = useState({});
    useEffect(() => {
        apiRef.current.setQuickFilterValues(searchText.split(/\b\W+\b/).filter((word) => word !== ''));
    }, [searchText]);
    const handleRowEditStop = (params, event) => {
        if (params.reason === GridRowEditStopReasons.rowFocusOut) {
            event.defaultMuiPrevented = true;
        }
    };
    const handleEditClick = (id) => () => {
        setRowModesModel({ ...rowModesModel, [id]: { mode: GridRowModes.Edit } });
    };
    const handleSaveClick = (id) => () => {
        setRowModesModel({ ...rowModesModel, [id]: { mode: GridRowModes.View } });
    };
    const handleDeleteClick = (id) => () => {
        setRows(rows.filter((row) => row.id !== id));
    };
    const handleCancelClick = (id) => () => {
        setRowModesModel({
            ...rowModesModel,
            [id]: { mode: GridRowModes.View, ignoreModifications: true },
        });
        const editedRow = rows.find((row) => row.id === id);
        if (editedRow.isNew) {
            setRows(rows.filter((row) => row.id !== id));
        }
    };
    const processRowUpdate = (newRow) => {
        const updatedRow = { ...newRow, isNew: false };
        setRows(rows.map((row) => (row.id === newRow.id ? updatedRow : row)));
        return updatedRow;
    };
    const handleRowModesModelChange = (newRowModesModel) => {
        setRowModesModel(newRowModesModel);
    };
    const columns = [
        {
            field: 'id',
            headerName: 'Order',
            minWidth: 80,
            flex: 1,
            resizable: false,
        },
        {
            field: 'client',
            headerName: 'Client',
            flex: 2,
            minWidth: 180,
            resizable: false,
            renderHeader: () => (_jsxs(Stack, { alignItems: "center", gap: 0.75, children: [_jsx(IconifyIcon, { icon: "mingcute:user-2-fill", color: "neutral.main", fontSize: "body2.fontSize" }), _jsx(Typography, { variant: "caption", mt: 0.25, letterSpacing: 0.5, children: "Client" })] })),
            valueGetter: (params) => {
                return `${params.name} ${params.email}`;
            },
            renderCell: (params) => {
                return (_jsxs(Stack, { direction: "column", alignSelf: "center", justifyContent: "center", sx: { height: 1 }, children: [_jsx(Typography, { variant: "subtitle1", fontSize: "caption.fontSize", children: params.row.client.name }), _jsx(Typography, { variant: "subtitle2", color: "text.secondary", fontSize: "caption.fontSize", children: params.row.client.email })] }));
            },
            sortComparator: (v1, v2) => v1.localeCompare(v2),
        },
        {
            field: 'date',
            type: 'date',
            headerName: 'Date',
            editable: true,
            minWidth: 100,
            flex: 1,
            resizable: false,
            renderHeader: () => (_jsxs(Stack, { alignItems: "center", gap: 0.75, children: [_jsx(IconifyIcon, { icon: "mdi:calendar", color: "neutral.main", fontSize: "body1.fontSize" }), _jsx(Typography, { mt: 0.175, variant: "caption", letterSpacing: 0.5, children: "Date" })] })),
            renderCell: (params) => format(new Date(params.value), 'MMM dd, yyyy'),
        },
        {
            field: 'status',
            headerName: 'Status',
            sortable: false,
            minWidth: 120,
            flex: 1,
            resizable: false,
            renderHeader: () => (_jsxs(Stack, { alignItems: "center", gap: 0.875, children: [_jsx(IconifyIcon, { icon: "carbon:checkbox-checked-filled", color: "neutral.main", fontSize: "body1.fontSize" }), _jsx(Typography, { mt: 0.175, variant: "caption", letterSpacing: 0.5, children: "Status" })] })),
            renderCell: (params) => {
                return (_jsx(Stack, { direction: "column", alignSelf: "center", justifyContent: "center", sx: { height: 1 }, children: _jsx(StatusChip, { status: params.value }) }));
            },
            renderEditCell: (params) => {
                const handleChange = (event) => {
                    params.api.setEditCellValue({
                        id: params.id,
                        field: params.field,
                        value: event.target.value,
                    });
                };
                return (_jsxs(Select, { value: params.value, onChange: handleChange, fullWidth: true, children: [_jsx(MenuItem, { value: "delivered", children: "Delivered" }), _jsx(MenuItem, { value: "pending", children: "Pending" }), _jsx(MenuItem, { value: "canceled", children: "Canceled" })] }));
            },
            editable: true,
        },
        {
            field: 'country',
            headerName: 'Country',
            sortable: false,
            flex: 1,
            minWidth: 120,
            resizable: false,
            editable: true,
            renderHeader: () => (_jsxs(Stack, { alignItems: "center", gap: 0.75, children: [_jsx(IconifyIcon, { icon: "healthicons:geo-location", color: "neutral.main", fontSize: "h5.fontSize" }), _jsx(Typography, { mt: 0.175, variant: "caption", letterSpacing: 0.5, children: "Country" })] })),
        },
        {
            field: 'total',
            headerName: 'Total',
            headerAlign: 'right',
            align: 'right',
            sortable: false,
            minWidth: 120,
            flex: 1,
            resizable: false,
        },
        {
            field: 'actions',
            type: 'actions',
            headerName: '',
            minWidth: 120,
            flex: 1,
            cellClassName: 'actions',
            resizable: false,
            getActions: ({ id }) => {
                const isInEditMode = rowModesModel[id]?.mode === GridRowModes.Edit;
                if (isInEditMode) {
                    return [
                        _jsx(GridActionsCellItem, { icon: _jsx(IconifyIcon, { color: "primary.main", icon: "mdi:content-save", sx: { fontSize: 'body1.fontSize', pointerEvents: 'none' } }), label: "Save", onClick: handleSaveClick(id), size: "small" }),
                        _jsx(GridActionsCellItem, { icon: _jsx(IconifyIcon, { color: "text.secondary", icon: "iconamoon:sign-times-duotone", sx: { fontSize: 'body1.fontSize', pointerEvents: 'none' } }), label: "Cancel", onClick: handleCancelClick(id), size: "small" }),
                    ];
                }
                return [
                    _jsx(GridActionsCellItem, { icon: _jsx(IconifyIcon, { icon: "fluent:edit-32-filled", color: "text.secondary", sx: { fontSize: 'body1.fontSize', pointerEvents: 'none' } }), label: "Edit", onClick: handleEditClick(id), size: "small" }),
                    _jsx(GridActionsCellItem, { icon: _jsx(IconifyIcon, { icon: "mingcute:delete-3-fill", color: "text.secondary", sx: { fontSize: 'body1.fontSize', pointerEvents: 'none' } }), label: "Delete", onClick: handleDeleteClick(id), size: "small" }),
                ];
            },
        },
    ];
    return (_jsx(DataGrid, { apiRef: apiRef, rows: rows, columns: columns, rowHeight: 80, editMode: "row", initialState: {
            pagination: {
                paginationModel: {
                    pageSize: 6,
                },
            },
        }, checkboxSelection: true, pageSizeOptions: [6], disableColumnMenu: true, disableVirtualization: true, disableRowSelectionOnClick: true, rowModesModel: rowModesModel, onRowModesModelChange: handleRowModesModelChange, onRowEditStop: handleRowEditStop, processRowUpdate: processRowUpdate, slots: {
            pagination: DataGridFooter,
        }, slotProps: {
            toolbar: { setRows, setRowModesModel },
        } }));
};
export default OrdersStatusTable;
