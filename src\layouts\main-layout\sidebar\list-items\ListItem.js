import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import Link from '@mui/material/Link';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';
import IconifyIcon from 'components/base/IconifyIcon';
const ListItem = ({ subheader, icon, path, active }) => {
    const [open, setOpen] = useState(false);
    const handleClick = () => {
        setOpen(!open);
    };
    return (_jsxs(ListItemButton, { component: Link, href: path, onClick: handleClick, sx: { opacity: active ? 1 : 0.3 }, children: [_jsx(ListItemIcon, { children: icon && (_jsx(IconifyIcon, { icon: icon, sx: {
                        color: active && path === '/' ? 'primary.main' : null,
                    } })) }), _jsx(ListItemText, { primary: subheader, sx: {
                    '& .MuiListItemText-primary': {
                        color: active && path === '/' ? 'primary.main' : null,
                    },
                } })] }));
};
export default ListItem;
