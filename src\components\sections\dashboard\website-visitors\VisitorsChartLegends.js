import { jsx as _jsx } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { useTheme } from '@mui/material';
import Stack from '@mui/material/Stack';
import VisitorsChartLegend from './VisitorsChartLegend';
export const legendsData = [
    {
        id: 1,
        type: 'Organic',
        rate: '80%',
    },
    {
        id: 2,
        type: 'Social',
        rate: '60%',
    },
    {
        id: 3,
        type: 'Direct',
        rate: '50%',
    },
];
const VisitorsChartLegends = ({ chartRef }) => {
    const theme = useTheme();
    const [toggleColor, setToggleColor] = useState({
        organic: true,
        social: true,
        direct: true,
    });
    useEffect(() => {
        const handleBodyClick = (e) => {
            handleToggleLegend(e, null);
        };
        document.body.addEventListener('click', handleBodyClick);
        return () => {
            document.body.removeEventListener('click', handleBodyClick);
        };
    }, []);
    const getActiveColor = (type) => {
        if (type === 'Organic') {
            return theme.palette.primary.main;
        }
        else if (type === 'Social') {
            return theme.palette.secondary.lighter;
        }
        else if (type === 'Direct') {
            return theme.palette.secondary.main;
        }
    };
    const getDisableColor = (type) => {
        if (type === 'Organic') {
            return theme.palette.primary.dark;
        }
        else if (type === 'Social') {
            return theme.palette.secondary.darker;
        }
        else if (type === 'Direct') {
            return theme.palette.secondary.dark;
        }
    };
    const handleToggleLegend = (e, type) => {
        e.stopPropagation();
        const echartsInstance = chartRef.current?.getEchartsInstance();
        if (!echartsInstance)
            return;
        const option = echartsInstance.getOption();
        if (type === 'Organic') {
            setToggleColor({ organic: true, social: false, direct: false });
        }
        else if (type === 'Social') {
            setToggleColor({ organic: false, social: true, direct: false });
        }
        else if (type === 'Direct') {
            setToggleColor({ organic: false, social: false, direct: true });
        }
        else {
            setToggleColor({ organic: true, social: true, direct: true });
        }
        if (Array.isArray(option.series)) {
            const series = option.series.map((s) => {
                if (Array.isArray(s.data)) {
                    s.data.forEach((item) => {
                        if (type !== null && item.itemStyle && item.itemStyle.color) {
                            if (type === item.type) {
                                item.itemStyle.color = getActiveColor(item.type);
                            }
                            else {
                                item.itemStyle.color = getDisableColor(item.type);
                            }
                        }
                        else {
                            item.itemStyle.color = getActiveColor(item.type);
                        }
                    });
                }
                return s;
            });
            echartsInstance.setOption({ series });
        }
    };
    return (_jsx(Stack, { mt: -1, spacing: 3, direction: "column", children: legendsData.map((item) => (_jsx(VisitorsChartLegend, { data: item, toggleColor: toggleColor, handleToggleLegend: handleToggleLegend }, item.id))) }));
};
export default VisitorsChartLegends;
