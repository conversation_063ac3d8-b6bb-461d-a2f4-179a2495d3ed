export interface CouponDto {
    _id: string;
    code: string;
    discountType: string;
    discountValue: number;
    totalCoupons: number;
    minOrderAmount: number;
    couponUserType: string;
    expirationDate: string;
    applicableCategories: string[];
    appliesToAllCategories: boolean;
    isActive: boolean;
    createdAt: string;
}

export interface CouponsResponseDtp {
    status: string;
    results: number;
    data: CouponDto[];
}