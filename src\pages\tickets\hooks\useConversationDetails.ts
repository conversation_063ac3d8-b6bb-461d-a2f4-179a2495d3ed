import {useQuery} from "react-query";
import {useSearchParams} from "react-router-dom";
import { showError, useQueryFix } from 'vbrae-utils';
import { getConversationById } from 'pages/tickets/api/getConversationById.ts';

export const useConversationDetails = () => {

  const [searchParams] = useSearchParams();
  const conversationId = searchParams.get('conversationId') ?? "";

  const { data: conversation, loading: conversationLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['conversations', conversationId],
      queryFn: () => getConversationById({conversationId}),
      onError: showError,
      refetchOnWindowFocus: false,
      enabled: !!conversationId,
    }),
    transform: (data) => data,
  });

  return { conversation, conversationLoading};
};