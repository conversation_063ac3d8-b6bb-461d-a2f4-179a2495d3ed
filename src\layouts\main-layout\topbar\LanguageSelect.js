import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState } from 'react';
import Menu from '@mui/material/Menu';
import Tooltip from '@mui/material/Tooltip';
import MenuItem from '@mui/material/MenuItem';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import ListItemText from '@mui/material/ListItemText';
import ListItemIcon from '@mui/material/ListItemIcon';
import IconifyIcon from 'components/base/IconifyIcon';
const languages = [
    {
        id: 1,
        code: 'eng',
        lang: 'English',
        flag: 'twemoji:flag-united-kingdom',
    },
    {
        id: 2,
        code: 'en-US',
        lang: 'English (US)',
        flag: 'twemoji:flag-united-states',
    },
    {
        id: 3,
        code: 'ban',
        lang: 'বাংলা',
        flag: 'twemoji:flag-bangladesh',
    },
    {
        id: 4,
        code: 'zh',
        lang: '中文',
        flag: 'twemoji:flag-china',
    },
    {
        id: 5,
        code: 'tr',
        lang: 'Türkçe',
        flag: 'twemoji:flag-turkey',
    },
];
const LanguageSelect = () => {
    const [language, setLanguage] = useState(languages[0]);
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);
    const handleFlagButtonClick = (event) => {
        setAnchorEl(event.currentTarget);
    };
    const handleFlagMenuClose = () => {
        setAnchorEl(null);
    };
    const handleLanguageItemClick = (langItem) => {
        setLanguage(langItem);
        handleFlagMenuClose();
    };
    return (_jsxs(_Fragment, { children: [_jsx(Tooltip, { title: `${language.lang} - ${language.code}`, children: _jsx(IconButton, { onClick: handleFlagButtonClick, sx: { fontSize: 'h4.fontSize' }, children: _jsx(IconifyIcon, { icon: language.flag }) }) }), _jsx(Menu, { anchorEl: anchorEl, id: "account-menu", open: open, onClose: handleFlagMenuClose, onClick: handleFlagMenuClose, PaperProps: {
                    elevation: 0,
                    sx: {
                        mt: 1.5,
                        p: '0 !important',
                        width: 240,
                        overflow: 'hidden',
                    },
                }, transformOrigin: { horizontal: 'right', vertical: 'top' }, anchorOrigin: { horizontal: 'right', vertical: 'bottom' }, children: languages.map((langItem) => {
                    return (_jsxs(MenuItem, { onClick: () => handleLanguageItemClick(langItem), children: [_jsx(ListItemIcon, { sx: { mr: 2, fontSize: 'h3.fontSize' }, children: _jsx(IconifyIcon, { icon: langItem.flag }) }), _jsx(ListItemText, { children: _jsx(Typography, { children: langItem.lang }) }), _jsx(ListItemText, { children: _jsx(Typography, { textAlign: "right", children: langItem.code }) })] }, langItem.id));
                }) })] }));
};
export default LanguageSelect;
