import * as Yup from 'yup';
import { useMutation } from 'react-query';
import { useFormik } from 'formik';
import { showError, showSuccess } from 'vbrae-utils';
import { postCoupon } from 'pages/coupons/api/postCoupon.ts';
import { CouponDto } from 'pages/coupons/types/coupon.ts';
import {useEffect, useState} from 'react';
import {patchCoupon} from "pages/coupons/api/patchCoupon.ts";

export type ErrorType = {
  message: string;
};

const couponSchema = Yup.object().shape({
    code:Yup.string().trim(),
    discountValue: Yup.string(),
    totalCoupons: Yup.string(),
    minOrderAmount: Yup.string(),
    couponUserType: Yup.string(),
    expirationDate: Yup.string(),
    applicableCategories: Yup.array(),
    appliesToAllCategories: Yup.boolean(),
});

export type InitialValuesDto = {
    discountType: string;
    isActive: boolean;
    code: string;
    discountValue: string;
    totalCoupons: string;
    minOrderAmount: string;
    couponUserType: string;
    expirationDate: string;
    applicableCategories: string[];
    appliesToAllCategories: boolean;
};

export default function useCouponForm({couponDetails}: {couponDetails?: CouponDto}) {
    const [initialValues, setInitialState] = useState<InitialValuesDto>({
        code: '',
        discountValue: '',
        totalCoupons: '',
        minOrderAmount: '',
        couponUserType: 'single',
        expirationDate: '',
        applicableCategories: [],
        appliesToAllCategories: false,

        discountType: "percentage",
        isActive: true,

    });

    useEffect(() => {
        if(!couponDetails) return;

        setInitialState({
            code: couponDetails.code,
            discountValue: couponDetails.discountValue.toString(),
            totalCoupons: couponDetails.totalCoupons.toString(),
            minOrderAmount: couponDetails.minOrderAmount.toString(),
            couponUserType: couponDetails.couponUserType,
            expirationDate: couponDetails.expirationDate,
            applicableCategories: couponDetails.applicableCategories,
            appliesToAllCategories: couponDetails.appliesToAllCategories,

            discountType: "percentage",
            isActive: true,
        })
    }, [couponDetails]);

    const { mutateAsync } = useMutation(postCoupon, {
        onError: (error:ErrorType)=>showError(error),
    });

    const { mutateAsync:updateAsync } = useMutation(patchCoupon, {
        onError: (error:ErrorType)=>showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: couponSchema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);
            let response;
            if(couponDetails) response = await updateAsync({ _id: couponDetails._id, ...values });
            else response = await mutateAsync(values);
            setSubmitting(false);
            !couponDetails && resetForm();
            if (response) {
                showSuccess(`Coupon ${couponDetails ? 'updated' : 'added'} successfully`)
            }
        },
    });

    return { formik };
}
