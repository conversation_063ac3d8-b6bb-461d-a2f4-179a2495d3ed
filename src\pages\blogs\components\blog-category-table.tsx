import { TableComponent } from 'components/table/table.tsx';
import { useEffect, useMemo, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import {InputBase, InputLabel} from '@mui/material';
import IconButton from '@mui/material/IconButton';
import SearchIcon from '@mui/icons-material/Search';
import Paper from '@mui/material/Paper';
import { fontFamily } from 'theme/typography.ts';
import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import BasicDropdown from 'components/dropdown.tsx';
import useBlogCategories from 'pages/blogs/hooks/useBlogCategories.ts';
import { BlogCategory } from 'pages/blogs/types/blog-category.ts';
import useDeleteBlogCategory from 'pages/blogs/hooks/useDeleteBlogCategory.ts';
import SelectMenu, {SelectedValue} from "components/select-menu.tsx";
import {languages} from "pages/category/const/languages.ts";
import {useNavigate} from "react-router-dom";

export default function BlogCategoryTable() {
  const navigate = useNavigate();
  const [searchValue, setSearchValue] = useState('');
  const [productToDelete, setProductToDelete] = useState({ _id: '' });
  const [selectedLanguage, setSelectedLanguage] = useState<SelectedValue>(languages[0]);
  const { categories, categoryLoading } = useBlogCategories({ language: selectedLanguage._id as string });
  const { categoryDelete } = useDeleteBlogCategory(productToDelete);

  useEffect(() => {
    if (!productToDelete._id) return;

      categoryDelete().finally(() => setProductToDelete({ _id: '' }));
  }, [productToDelete]);

  const columns = useMemo<ColumnDef<BlogCategory>[]>(
    () => [
      {
        header: 'Name',
        accessorFn: (row) => row.categoryName,
        id: 'categoryName',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Language',
        accessorFn: (row) => row.language,
        id: 'language',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Order',
        accessorFn: (row) => row.order,
        id: 'name',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Actions',
        accessorFn: () => {},
        id: 'actions',
        cell: (info) => {
          const options = [
            {
              title: 'Edit',
              action: () => navigate(`/blogs/edit-category/${info.row.original._id}`),
            },
            {
              title: 'Delete',
              action: () => setProductToDelete({ _id: info.row.original._id }),
            },
          ];
          return <BasicDropdown options={options} />;
        },
        enableGlobalFilter: false,
      },
    ],
    [],
  );

  if (categoryLoading || !categories) return <CircularProgress />;
  return (
      <>
          <Stack direction="column" spacing={2}>
              <Typography
                  variant="h5"
                  fontWeight={600}
                  letterSpacing={1}
                  fontFamily={fontFamily.workSans}
                  display={{ xs: 'none', lg: 'block' }}
              >
                  Blog Categories
              </Typography>
          </Stack>

          <Stack direction="row" gap={2} justifyContent="space-between" alignItems="end">
              <Box width={{ xs: "100%", sm: "200px" }}>
                  <InputLabel
                      component="label"
                      sx={{ fontSize: '12px', marginBottom: '20px' }}
                      size="small"
                      htmlFor="language"
                  >
                      Language
                  </InputLabel>
                  <SelectMenu
                      id="language"
                      value={selectedLanguage}
                      handleChange={(_, value) => setSelectedLanguage(value!)}
                      options={languages}
                  />
              </Box>
              <Box>
                  <Paper
                      component="form"
                      sx={{
                          p: '2px 4px',
                          display: 'flex',
                          alignItems: 'end',
                          justifyContent: 'center',
                          width: { xs: '100%', sm: 300 },
                      }}
                  >
                      <InputBase
                          sx={{ ml: 1, flex: 1, border: 'none' }}
                          placeholder="Search here"
                          inputProps={{ 'aria-label': 'search' }}
                          onChange={(e) => setSearchValue(e.target.value.trim())}
                      />
                      <IconButton type="button" sx={{ p: '10px' }} aria-label="search">
                          <SearchIcon />
                      </IconButton>
                  </Paper>
              </Box>
          </Stack>

          <Box sx={{ overflowX: 'auto', width: '100%' }}>
              <TableComponent
                  columns={columns}
                  data={categories}
                  globalFilter={searchValue}
                  setGlobalFilter={setSearchValue}
              />
          </Box>
      </>
  );
}