import {SelectedValue} from "components/select-menu.tsx";

export const generateQueryString = (selectedCategory: SelectedValue, selectedSubCategory: SelectedValue, selectedStock: SelectedValue, staticString?: string) => {
    let queryString = '';

    if (selectedCategory._id) {
        queryString += `category=${selectedCategory._id}`;
    }

    if (selectedSubCategory._id) {
        if (queryString) queryString += '&'
        queryString += `subCategory=${selectedSubCategory._id}`;
    }

    if (selectedStock._id) {
        if (queryString) queryString += '&';
        queryString += selectedStock._id;
    }

    if(staticString){
        if (queryString) queryString += '&';
        queryString += staticString;
    }

    return queryString;
};