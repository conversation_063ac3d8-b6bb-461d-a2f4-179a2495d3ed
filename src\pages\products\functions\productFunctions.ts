import {SelectedValue} from "components/select-menu.tsx";

export const generateQueryString = (selectedCategory: SelectedValue, selectedSubCategory: SelectedValue, selectedStock: SelectedValue, staticString?: string) => {
    const queryParts: string[] = [];

    // Add category filter if valid
    if (selectedCategory._id && selectedCategory._id !== null && selectedCategory._id !== '') {
        queryParts.push(`category=${encodeURIComponent(selectedCategory._id)}`);
    }

    // Add subcategory filter if valid
    if (selectedSubCategory._id && selectedSubCategory._id !== null && selectedSubCategory._id !== '') {
        queryParts.push(`subCategory=${encodeURIComponent(selectedSubCategory._id)}`);
    }

    // Add stock filter if valid
    if (selectedStock._id && selectedStock._id !== null && selectedStock._id !== '') {
        queryParts.push(String(selectedStock._id));
    }

    // Add static string if provided
    if (staticString && staticString.trim()) {
        queryParts.push(staticString.trim());
    }

    return queryParts.filter(Boolean).join('&');
};