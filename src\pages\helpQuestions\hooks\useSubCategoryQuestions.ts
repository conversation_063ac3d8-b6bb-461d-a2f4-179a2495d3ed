import { getQuestionBySubcategory } from 'pages/helpQuestions/api/getQuestionBySubcategory.ts';

import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';

type PropsDto = {
  id: string;
  subCategoryId: string;
}

export default function useSubCategoryQuestions(props: PropsDto) {
  const { data, loading } = useQueryFix({
    query: useQuery({
      queryKey: ['sub-category-questions', { ...props }],
      queryFn: () => getQuestionBySubcategory(props),
      onError: showError,
    }),
    transform: (data) => data,
  });
  return { data, loading };
}
