import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import { FormControl, InputLabel, Radio, RadioGroup } from '@mui/material';
import SelectMenu from 'components/select-menu.tsx';
import Box from '@mui/material/Box';
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import CustomAccordion from 'components/accordian.tsx';
import { useEffect, useState } from 'react';
import { useNavigate } from "react-router-dom";
import useCategories from "pages/category/hooks/useCategories.ts";
import useDeleteCategory from "pages/category/hooks/useDeleteCategory.ts";
import { languages } from "pages/category/const/languages.ts";
import generateAccordion from "pages/category/utils/misc.ts";
const label = { inputProps: { 'aria-label': 'Checkbox demo' } };
export default function CategoryView() {
    const navigate = useNavigate();
    const { categories, categoryLoading } = useCategories();
    const [selectedCategory, setSelectedCategory] = useState({ _id: '0', name: "All" });
    const [selectedLanguage, setSelectedLanguage] = useState(languages[0]);
    const [categoryId, setCategoryId] = useState("");
    const [accordionData, setAccordionData] = useState([]);
    const { categoryDelete } = useDeleteCategory({ _id: categoryId });
    const handleChange = (event, id) => {
        console.log(event, id);
    };
    useEffect(() => {
        if (!categoryId)
            return;
        categoryDelete().finally();
    }, [categoryId]);
    useEffect(() => {
        if (!categories)
            return;
        setAccordionData(generateAccordion(categories));
    }, [categories]);
    if (categoryLoading || !categories) {
        return _jsx("div", { children: "Loading..." });
    }
    const handleSelectChange = (id, value) => {
        if (!value)
            return;
        if (id === 'parent_category') {
            if (value._id === '0') {
                setSelectedCategory({ _id: '0', name: "All" });
                setAccordionData(generateAccordion(categories));
                return;
            }
            const filterData = categories.filter(item => item._id === value._id);
            const accData = generateAccordion(filterData);
            setAccordionData(accData);
            value && setSelectedCategory(value);
        }
        else if (id === 'language') {
            value && setSelectedLanguage(value);
        }
    };
    return (_jsxs(Stack, { spacing: 2, direction: "column", children: [_jsxs(Paper, { elevation: 3, sx: { py: 4, width: '100%' }, children: [_jsxs(Stack, { direction: "row", alignItems: "center", justifyContent: "space-between", gap: 2, children: [_jsx(Typography, { variant: "h5", fontWeight: 600, children: "Categories" }), _jsx(Button, { type: "submit", variant: "contained", size: "small", children: "Add Category" })] }), _jsxs(Stack, { direction: "row", alignItems: "center", gap: 2, children: [_jsxs(Box, { width: "200px", children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "language", children: "Language" }), _jsx(SelectMenu, { id: "language", value: selectedLanguage, handleChange: handleSelectChange, options: languages })] }), _jsxs(Box, { width: "300px", children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "parent_category", children: "Parent Category" }), _jsx(SelectMenu, { value: selectedCategory, id: "parent_category", handleChange: handleSelectChange, options: [{ _id: '0', name: "All" }, ...categories.map(item => ({
                                                _id: item._id,
                                                name: item.categoryName.en,
                                            }))] })] })] }), _jsx(CustomAccordion, { data: accordionData, onParentDelete: ({ _id }) => setCategoryId(String(_id)), onChildDelete: ({ _id }) => setCategoryId(String(_id)), onParentEdit: ({ _id }) => navigate(`/category/${_id}`), onChildEdit: ({ _id }) => navigate(`/category/${_id}`) })] }), _jsx(Paper, { elevation: 3, sx: { py: 4, width: '50%' }, children: _jsxs(Stack, { spacing: 2, direction: "column", children: [_jsx(Typography, { variant: "h5", fontWeight: 600, children: "Settings" }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px', width: "50%" }, size: "small", htmlFor: "sort_categories", children: "Sort Categories" }), _jsx(FormControl, { children: _jsxs(RadioGroup, { row: true, "aria-labelledby": "demo-row-radio-buttons-group-label", onChange: (e) => handleChange(e, 'sortBy'), children: [_jsx(FormControlLabel, { value: "category_order", control: _jsx(Radio, {}), label: "By Category Order" }), _jsx(FormControlLabel, { value: "date_asc", control: _jsx(Radio, {}), label: "By Date" }), _jsx(FormControlLabel, { value: "date_desc", control: _jsx(Radio, {}), label: "By Date (Desc)" }), _jsx(FormControlLabel, { value: "alphabetically", control: _jsx(Radio, {}), label: "Alphabetically" })] }) })] }), _jsxs(Box, { sx: { display: "flex", gap: "3px", alignItems: "center" }, children: [_jsx(Typography, { variant: "h6", fontSize: 14, fontWeight: 400, children: "Sort Parent Categories by Category Order" }), _jsx(Checkbox, { ...label, defaultChecked: true })] }), _jsx(Button, { type: "submit", variant: "contained", size: "small", sx: { width: "fit-content", marginLeft: 'auto', paddingX: 4 }, children: "Save Changes" })] }) })] }));
}
