import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import getAllGiveAway from 'pages/give-away/api/getAllGiveAway.ts';

export default function useGiveAway() {
  const { data: giveAway, loading: giveAwayLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['giveAway'],
      queryFn: () => getAllGiveAway(),
      onError: showError,
    }),
    transform: (response) => response.data,
  });
  return { giveAway, giveAwayLoading };
}
