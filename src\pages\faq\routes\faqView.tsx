import { useNavigate } from 'react-router-dom';
import { useEffect, useMemo, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import BasicDropdown from 'components/dropdown.tsx';
import CircularProgress from '@mui/material/CircularProgress';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { fontFamily } from 'theme/typography.ts';
import Box from '@mui/material/Box';
import { InputBase } from '@mui/material';
import Paper from '@mui/material/Paper';
import IconButton from '@mui/material/IconButton';
import { TableComponent } from 'components/table/table.tsx';
import SearchIcon from '@mui/icons-material/Search';
import useDeleteFaq from 'pages/faq/hooks/useDeleteFaq.ts';
import useFaq from 'pages/faq/hooks/useFaq.ts';
import { FAQ } from 'pages/faq/types/FAQ.ts';
import Button from "@mui/material/Button";

export default function FaqView() {
  const navigate = useNavigate();
  const [searchValue, setSearchValue] = useState('');
  const [faqToDelete, setFaqToDelete] = useState({ _id: '' });
  const { faq, faqLoading } = useFaq();
  const { faqDelete } = useDeleteFaq(faqToDelete);

  useEffect(() => {
    if (!faqToDelete._id) return;

    faqDelete().finally(() => setFaqToDelete({ _id: '' }));
  }, [faqToDelete]);

  const columns = useMemo<ColumnDef<FAQ>[]>(
    () => [
      {
        header: 'Title',
        accessorFn: (row) => row.title,
        id: 'title',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Content',
        accessorFn: (row) => row.content,
        id: 'content',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Order',
        accessorFn: (row) => row.order,
        id: 'order',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Actions',
        accessorFn: () => {},
        id: 'actions',
        cell: (info) => {
          const options = [
            {
              title: 'Edit',
              action: () => navigate(`/faq/edit/${info.row.original._id}`),
            },
            {
              title: 'Delete',
              action: () => setFaqToDelete({ _id: info.row.original._id }),
            },
          ];
          return <BasicDropdown options={options} />;
        },
        enableGlobalFilter: false,
      },
    ],
    [],
  );

  if (faqLoading || !faq) return <CircularProgress />;

  return (
    <>
      <Stack direction="column" spacing={2}>
        <Typography
          variant="h5"
          fontWeight={600}
          letterSpacing={1}
          fontFamily={fontFamily.workSans}
          display={{ xs: 'none', lg: 'block' }}
        >
          FAQ
        </Typography>
      </Stack>

      <Stack direction="row" gap={2} justifyContent="space-between" alignItems="end">
          <Button type="submit" variant="contained" size="small" onClick={() => navigate(`/faq/add`)}>
              Add Faq
          </Button>
          <Box>
              <Paper
                  component="form"
                  sx={{
                      p: '2px 4px',
                      display: 'flex',
                      alignItems: 'end',
                      justifyContent: 'center',
                      width: { xs: '100%', sm: 300 },
                  }}
              >
                  <InputBase
                      sx={{ ml: 1, flex: 1, border: 'none' }}
                      placeholder="Search here"
                      inputProps={{ 'aria-label': 'search' }}
                      onChange={(e) => setSearchValue(e.target.value.trim())}
                  />
                  <IconButton type="button" sx={{ p: '10px' }} aria-label="search">
                      <SearchIcon />
                  </IconButton>
              </Paper>
          </Box>
      </Stack>

      <Box sx={{ overflowX: 'auto', width: '100%' }}>
        <TableComponent
          columns={columns}
          data={faq}
          globalFilter={searchValue}
          setGlobalFilter={setSearchValue}
        />
      </Box>
    </>
  );
}