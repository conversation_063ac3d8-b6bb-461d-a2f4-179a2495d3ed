import {TemplateDto} from "pages/templates/types/template.ts";

interface Seller {
    _id: string;
    name: string;
    email: string;
    ipAddress: string;
    role: string;
    createdAt: string;
}

export interface Product {
    _id: string;
    template: null | TemplateDto;
    seller: Seller;
    category: string;
    subcategory: string;
    region: string;
    expectedPrice: number;
    customerPays: number;
    instantDelivery: boolean;
    licenseKeys: string[];
    stock: number;
    active: boolean;
    extraCharge: number;
    pageViews: number;
    draft: boolean;
    status: boolean;
    visibility: boolean;
    featured: boolean;
    specialOffer: boolean;
    addedFee: number;
    createdAt: string;
    updatedAt: string;
}
