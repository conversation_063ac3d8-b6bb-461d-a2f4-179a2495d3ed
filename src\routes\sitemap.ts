import { Location } from "react-router-dom";

export interface SubMenuItem {
    name: string;
    pathName: string;
    path: string;
    active?: boolean;
    items?: SubMenuItem[];
}

export interface MenuItem {
    id: string;
    subheader: string;
    path?: string;
    icon?: string;
    avatar?: string;
    active?: boolean;
    items?: SubMenuItem[];
    location?: Location;
}

export const sideMenuTop = [
    {
        id: 'dashboard',
        subheader: 'Dashboard',
        icon: 'mingcute:home-2-fill',
        path: '/',
    },
    {
        id: 'tax-manager',
        subheader: 'Tax Management',
        icon: 'mdi:calculator-variant',
        path: '/tax-manager',
    },
    {
        id: 'levels',
        subheader: 'Seller Levels',
        icon: 'mdi:calculator-variant',
        path: '/levels',
    },
    {
        id: 'give-away',
        subheader: 'Give Away',
        icon: 'mdi:calculator-variant',
        path: '/give-away',
    },
    {
        id: 'homepage-manager',
        subheader: 'Homepage Manager',
        icon: 'mingcute:layout-4-line',
        path: '/homepage-manager',
    },
    {
        id: 'coupon-manager',
        subheader: 'Coupons Manager',
        icon: 'mingcute:ticket-line',
        path: '/coupons',
    },
    {
        id: 'shop-request',
        subheader: 'Shop Opening Request',
        icon: 'mingcute:shop-line',
        path: '/shop-request',
    },
    {
        id: 'shop-request',
        subheader: 'Faq',
        icon: 'mingcute:shop-line',
        path: '/faq',
    },
    {
        id: 'shop-request',
        subheader: 'Tickets',
        icon: 'mingcute:shop-line',
        path: '/tickets',
    },
    {
        id: 'help-questions',
        subheader: 'Help Questions',
        icon: 'mingcute:shop-line',
        path: '/helpQuestions',
    },
];

export const categoryOptions = [
    {
        id: 'categories',
        subheader: 'Categories',
        icon: 'mingcute:folder-3-line',
        items: [
            {
                name: 'Categories',
                pathName: 'categories',
                path: '/category',
            },
            {
                name: 'Add Categories',
                pathName: 'add-categories',
                path: '/category/add',
            },
            {
                name: 'Bulk Category Upload',
                pathName: 'bulk-categories-upload',
                path: '/category/upload',
            },
        ],
    },
    {
        id: 'blogs',
        subheader: 'Blogs',
        icon: 'mingcute:book-5-line', // Blog/Articles
        items: [
            {
                name: 'Add Post',
                pathName: 'add-blog',
                path: '/blogs/add',
            },
            {
                name: 'Blogs',
                pathName: 'blogs',
                path: '/blogs',
            },
            {
                name: 'Categories',
                pathName: 'categories',
                path: '/blogs/categories',
            },
        ],
    },
    {
        id: 'templates',
        subheader: 'Templates',
        icon: 'mingcute:copy-2-line', // Templates/Documents
        items: [
            {
                name: 'List',
                pathName: 'List',
                path: '/templates/list',
            },
            {
                name: 'Add Manually',
                pathName: 'Add-Manually',
                path: '/templates/add',
            },
            {
                name: 'Request',
                pathName: 'Request',
                path: '/templates/request',
            },
        ],
    },
    {
        id: 'orders',
        subheader: 'Orders',
        icon: 'mingcute:shopping-bag-2-line', // Orders/Shopping Bag
        items: [
            {
                name: 'Orders',
                pathName: 'orders',
                path: '/orders',
            },
        ],
    },
    {
        id: 'products',
        subheader: 'Products',
        icon: 'mingcute:box-3-line', // Products/Boxes
        items: [
            {
                name: 'Products',
                pathName: 'products',
                path: '/products',
            },
            {
                name: 'Special Offers',
                pathName: 'special-products',
                path: '/special-products',
            },
            {
                name: 'Deleted Products',
                pathName: 'deleted-products',
                path: '/deleted-products',
            },
        ],
    },
];
