import * as React from 'react';
import { <PERSON>, Button, DialogActions, InputLabel, TextField } from '@mui/material';
import Dialog from '@mui/material/Dialog';
import IconifyIcon from 'components/base/IconifyIcon.tsx';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import Stack from '@mui/material/Stack';
import useTaxForm from 'pages/tax/hooks/useTaxForm.ts';

type QuestionModalProps = {
  open: boolean;
  handleClose: () => void;
  state: {
    _id: string;
    taxPercentage: string;
    country: string;
  };
};

const TaxModal = ({ open, handleClose, state } : QuestionModalProps) => {
  const { formik } = useTaxForm({ existingTax: state, handleClose });

  return (
    <React.Fragment>
      <Dialog
        open={open}
        fullWidth={true}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <Stack direction="row" justifyContent="space-between">
          <DialogTitle sx={{ padding: 0 }} id="alert-dialog-title">
            {state._id ? "Update" : "Add"} Tax for {state.country}
          </DialogTitle>
          <DialogActions>
            <IconifyIcon icon="mingcute:close-line" onClick={handleClose} />
          </DialogActions>
        </Stack>
        <DialogContent sx={{ padding: 0 }}>
          <form onSubmit={formik.handleSubmit}>
            <Box sx={{ marginBottom: '20px' }}>
              <InputLabel
                component="label"
                sx={{ fontSize: '12px', marginBottom: '20px' }}
                size="small"
                htmlFor="title"
              >
                Tax Percentage
              </InputLabel>
              <TextField
                id="taxPercentage"
                type="text"
                variant="filled"
                placeholder="Tax Percentage"
                autoComplete="taxPercentage"
                fullWidth
                required
                {...formik.getFieldProps('taxPercentage')}
              />
            </Box>
            <Button
              type="submit"
              variant="contained"
              size="medium"
              sx={{ marginTop: '20px', width: '20%', marginLeft: 'auto' }}
            >
              {formik.isSubmitting ? 'Processing...' : "Submit"}
            </Button>
          </form>
        </DialogContent>
      </Dialog>
    </React.Fragment>
  );
};

export default TaxModal;
