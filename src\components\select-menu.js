import { jsx as _jsx } from "react/jsx-runtime";
import { FormControl } from '@mui/material';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
const SelectMenu = ({ id, value, handleChange, options }) => {
    return (_jsx(FormControl, { fullWidth: true, children: _jsx(Select, { id: id, value: value.name, onChange: (event) => handleChange(id, options.find(option => option.name === event.target.value)), children: options.map((option, index) => (_jsx(MenuItem, { value: option.name, children: option.name }, index))) }) }));
};
export default SelectMenu;
