import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import { InputLabel } from '@mui/material';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import useFaqDetails from 'pages/faq/hooks/useFaqDetails.ts';
import { useParams } from 'react-router-dom';
import useFaqForm from 'pages/faq/hooks/useFaqForm.ts';

export default function FaqEdit() {
  const { id = '' } = useParams();

  const { details } = useFaqDetails({ id });
  const { formik } = useFaqForm({ existingFaq: details });

  if (!details) return <CircularProgress />;

  return (
      <form onSubmit={formik.handleSubmit}>
          <Typography variant="h5" fontWeight={600} sx={{fontSize: {xs: '16px', sm: '18px'}}}>
              Edit Faq
          </Typography>

          <Box>
              <InputLabel
                  component="label"
                  sx={{fontSize: '12px', marginBottom: '20px'}}
                  size="small"
                  htmlFor="title"
              >
                  Title
              </InputLabel>
              <TextField
                  id="title"
                  type="text"
                  variant="filled"
                  placeholder="Title"
                  autoComplete="title"
                  fullWidth
                  required
                  {...formik.getFieldProps('title')}
              />
          </Box>

          <Box>
              <InputLabel
                  component="label"
                  sx={{fontSize: '12px', marginBottom: '20px'}}
                  size="small"
                  htmlFor="content"
              >
                  Content
              </InputLabel>
              <TextField
                  id="content"
                  type="text"
                  variant="filled"
                  placeholder="Content"
                  autoComplete="content"
                  fullWidth
                  required
                  {...formik.getFieldProps('content')}
              />
          </Box>

          <Box>
              <InputLabel
                  component="label"
                  sx={{fontSize: '12px', marginBottom: '20px'}}
                  size="small"
                  htmlFor="page"
              >
                  Page
              </InputLabel>
              <TextField
                  id="page"
                  type="text"
                  variant="filled"
                  placeholder="Page type"
                  autoComplete="page"
                  fullWidth
                  required
                  disabled={true}
                  {...formik.getFieldProps('page')}
              />
          </Box>

          <Box>
              <InputLabel
                  component="label"
                  sx={{fontSize: '12px', marginBottom: '20px'}}
                  size="small"
                  htmlFor="order"
              >
                  Order
              </InputLabel>
              <TextField
                  id="order"
                  type="number"
                  variant="filled"
                  placeholder="Order"
                  autoComplete="order"
                  required
                  fullWidth
                  {...formik.getFieldProps('order')}
              />
          </Box>

          <Button
              type="submit"
              variant="contained"
              size="medium"
              disabled={formik.isSubmitting}
              fullWidth
              sx={{marginTop: '20px'}}
          >
              {formik.isSubmitting ? 'Processing...' : 'Update Faq'}
          </Button>
      </form>
  );
}