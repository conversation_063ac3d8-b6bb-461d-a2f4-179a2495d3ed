export interface AccordionDto {
    _id: string;
    title: string;
    visibility: boolean,
    showInMainMenu: boolean,
    children: Accordion[]
}

export interface Accordion {
    title: string,
    _id: string,
    visibility: boolean,
    showInMainMenu: boolean
}


export interface AccordionQuestionDto {
    _id: string;
    title: string;
    content: string;
    isActive: boolean;
    createdAt: string;
};

export interface AccordionSubcategoryDto {
    _id: string;
    title: string;
    questions: AccordionQuestionDto[];
    isActive: boolean;
    createdAt: string;
};

export interface AccordionCategoryDto {
    _id: string;
    title: string;
    subcategories: AccordionSubcategoryDto[];
    isActive: boolean;
    createdAt: string;
    __v: number;
};