import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import getTicketDetails from 'pages/tickets/api/getTicketDetails.ts';

export type TicketPropsDto = {
  id: string;
};

export default function useTicketDetails(props:TicketPropsDto) {
    const { data: details, loading: detailsLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['ticket-details', {...props}],
            queryFn: () => getTicketDetails(props),
            onError: showError,
        }),
        transform: (data) => data.data,
    });
    return { details, detailsLoading };
}
