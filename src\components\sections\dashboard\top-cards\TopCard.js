import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { fontFamily } from 'theme/typography';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import IconifyIcon from 'components/base/IconifyIcon';
import RateChip from 'components/chips/RateChip';
const TopCard = (props) => {
    const { icon, title, value, rate, isUp } = props;
    return (_jsx(Grid, { item: true, xs: 12, sm: 6, xl: 3, children: _jsxs(Stack, { p: 2.25, pl: 2.5, direction: "column", component: Paper, gap: 1.5, height: 116, width: 1, children: [_jsxs(Stack, { justifyContent: "space-between", children: [_jsxs(Stack, { alignItems: "center", gap: 1, children: [_jsx(IconifyIcon, { icon: icon, color: "primary.main", fontSize: "h5.fontSize" }), _jsx(Typography, { variant: "subtitle2", color: "text.secondary", fontFamily: fontFamily.workSans, children: title })] }), _jsx(IconButton, { "aria-label": "menu", size: "small", sx: { color: 'neutral.light', fontSize: 'h5.fontSize' }, children: _jsx(IconifyIcon, { icon: "solar:menu-dots-bold" }) })] }), _jsxs(Stack, { alignItems: "center", gap: 0.875, children: [_jsx(Typography, { variant: "h3", fontWeight: 600, letterSpacing: 1, children: value }), _jsx(RateChip, { rate: rate, isUp: isUp })] })] }) }));
};
export default TopCard;
