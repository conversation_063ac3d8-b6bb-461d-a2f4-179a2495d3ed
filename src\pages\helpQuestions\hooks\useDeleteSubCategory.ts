import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { deleteSubCategory } from 'pages/helpQuestions/api/deleteSubCategory.ts';
import { useNavigate } from 'react-router-dom';

type SubcategoryPros = {
  categoryId: string;
  subcategoryId: string;
};

export default function useDeleteSubCategory(props: SubcategoryPros) {
    const navigate = useNavigate();
    const { refetch: subCategoryDelete, loading: subCategoryDelLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['delete-sub-category', { ...props }],
            queryFn: () => deleteSubCategory(props),
            onError: showError,
            onSuccess: () => navigate(-1),
            enabled: false,
        }),
        transform: (data) => data,
    });
    return { subCategoryDelete, subCategoryDelLoading };
}