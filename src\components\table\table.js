import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import './table.css';
import { flexRender, getCoreRowModel, getFilteredRowModel, getPaginationRowModel, useReactTable, } from '@tanstack/react-table';
import { rankItem } from '@tanstack/match-sorter-utils';
import Pagination from '@mui/material/Pagination';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Typography } from '@mui/material';
const fuzzyFilter = (row, columnId, value, addMeta) => {
    const rawValue = row.getValue(columnId);
    const stringValue = Array.isArray(rawValue) ? rawValue.join(' ') : String(rawValue || '');
    const itemRank = rankItem(stringValue, value);
    addMeta({
        itemRank,
    });
    return itemRank.passed;
};
export function TableComponent({ globalFilter, setGlobalFilter, data, columns, tableTitle, }) {
    const [columnResizeMode] = useState('onChange');
    const [columnResizeDirection] = useState('ltr');
    const table = useReactTable({
        data,
        columns: columns,
        debugTable: true,
        filterFns: {
            fuzzy: fuzzyFilter,
        },
        columnResizeMode,
        columnResizeDirection,
        state: {
            globalFilter
        },
        onGlobalFilterChange: setGlobalFilter,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        globalFilterFn: fuzzyFilter,
    });
    return (_jsxs("div", { children: [_jsxs(TableContainer, { component: Paper, sx: { overflowX: 'auto', borderRadius: 2, p: 2 }, children: [_jsx(Typography, { variant: "h6", sx: { fontWeight: 'bold', mb: 2 }, children: tableTitle }), _jsxs(Table, { sx: { minWidth: 650 }, children: [_jsx(TableHead, { children: table.getHeaderGroups().map((headerGroup) => (_jsx(TableRow, { children: headerGroup.headers.map((header) => (_jsx(TableCell, { sx: {
                                            textAlign: 'left',
                                            fontSize: '1rem',
                                            pr: 2.5,
                                            width: header.getSize(),
                                            fontWeight: 500,
                                        }, children: header.isPlaceholder ? null : (_jsx("div", { children: flexRender(header.column.columnDef.header, header.getContext()) })) }, header.id))) }, headerGroup.id))) }), _jsx(TableBody, { children: table.getRowModel().rows.map((row) => (_jsx(TableRow, { children: row.getVisibleCells().map((cell) => (_jsx(TableCell, { sx: {
                                            p: 2,
                                            pr: 2,
                                            fontSize: '1rem',
                                        }, children: _jsx("div", { children: flexRender(cell.column.columnDef.cell, cell.getContext()) }) }, cell.id))) }, row.id))) })] }), !table.getRowModel().rows.length && (_jsx(Typography, { variant: "h6", sx: { fontWeight: 'bold', mt: 4, textAlign: 'center' }, children: "Nothing here yet!" }))] }), table.getPageCount() > 1 && (_jsx("div", { style: { display: 'flex', justifyContent: 'center', marginTop: 16 }, children: _jsx(Pagination, { count: table.getPageCount(), page: table.getState().pagination.pageIndex + 1, onChange: (_, page) => table.setPageIndex(page - 1), siblingCount: 1, boundaryCount: 1, sx: { display: 'flex', justifyContent: 'center', gap: 2 } }) }))] }));
}
