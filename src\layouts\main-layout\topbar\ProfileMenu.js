import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState } from 'react';
import Menu from '@mui/material/Menu';
import Stack from '@mui/material/Stack';
import Avatar from '@mui/material/Avatar';
import Divider from '@mui/material/Divider';
import Tooltip from '@mui/material/Tooltip';
import MenuItem from '@mui/material/MenuItem';
import ButtonBase from '@mui/material/ButtonBase';
import Typography from '@mui/material/Typography';
import ListItemIcon from '@mui/material/ListItemIcon';
import IconifyIcon from 'components/base/IconifyIcon';
import AvatarImage from 'assets/images/avater.png';
import { useProfile } from "hooks/useProfile.ts";
import { clearAccessToken } from "vbrae-utils";
import { useNavigate } from "react-router-dom";
const ProfileMenu = () => {
    const { user } = useProfile();
    const navigate = useNavigate();
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);
    const handleProfileClick = (event) => {
        setAnchorEl(event.currentTarget);
    };
    const handleProfileMenuClose = () => {
        setAnchorEl(null);
    };
    const handleOptions = (action) => {
        action?.();
        setAnchorEl(null);
    };
    const menuItems = [
        {
            id: 1,
            title: 'View Profile',
            icon: 'mingcute:user-2-fill',
        },
        {
            id: 2,
            title: 'Account Settings',
            icon: 'material-symbols:settings-account-box-rounded',
        },
        {
            id: 3,
            title: 'Notifications',
            icon: 'ion:notifications',
        },
        {
            id: 4,
            title: 'Switch Account',
            icon: 'material-symbols:switch-account',
        },
        {
            id: 5,
            title: 'Help Center',
            icon: 'material-symbols:live-help',
        },
        {
            id: 6,
            title: 'Logout',
            icon: 'material-symbols:logout',
            action: () => {
                clearAccessToken();
                navigate('/auth/login');
            }
        },
    ];
    return (_jsxs(_Fragment, { children: [_jsx(Tooltip, { title: "Profile", children: _jsx(ButtonBase, { onClick: handleProfileClick, disableRipple: true, children: _jsxs(Stack, { spacing: 1, alignItems: "center", "aria-controls": open ? 'account-menu' : undefined, "aria-expanded": open ? 'true' : undefined, "aria-haspopup": "true", children: [_jsx(Avatar, { src: AvatarImage, sx: (theme) => ({
                                    ml: 0.8,
                                    height: 32,
                                    width: 32,
                                    bgcolor: theme.palette.primary.main,
                                }) }), user && _jsx(Typography, { variant: "subtitle2", children: user.name })] }) }) }), _jsxs(Menu, { anchorEl: anchorEl, id: "account-menu", open: open, onClose: handleProfileMenuClose, onClick: handleProfileMenuClose, PaperProps: {
                    elevation: 0,
                    sx: {
                        mt: 1.5,
                        p: '0 !important',
                        width: 240,
                        overflow: 'hidden',
                        '& .MuiAvatar-root': {
                            width: 34,
                            height: 34,
                            ml: -0.5,
                            mr: 1,
                        },
                    },
                }, transformOrigin: { horizontal: 'right', vertical: 'top' }, anchorOrigin: { horizontal: 'right', vertical: 'bottom' }, children: [_jsxs(MenuItem, { onClick: handleProfileMenuClose, sx: { '&:hover': { bgcolor: 'info.main' } }, children: [_jsx(Avatar, { src: AvatarImage, sx: {
                                    bgcolor: 'primary.main',
                                } }), _jsxs(Stack, { direction: "column", children: [_jsx(Typography, { variant: "body2", fontWeight: 500, children: "John Carter" }), _jsx(Typography, { variant: "caption", fontWeight: 400, color: "text.secondary", children: "<EMAIL>" })] })] }), _jsx(Divider, {}), menuItems.map((item) => {
                        return (_jsxs(MenuItem, { onClick: () => handleOptions(item.action), sx: { py: 1 }, children: [_jsx(ListItemIcon, { sx: { mr: 2, fontSize: 'button.fontSize' }, children: _jsx(IconifyIcon, { icon: item.icon }) }), _jsx(Typography, { variant: "body2", color: "text.secondary", children: item.title })] }, item.id));
                    })] })] }));
};
export default ProfileMenu;
