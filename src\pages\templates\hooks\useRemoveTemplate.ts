import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery, useQueryClient } from 'react-query';
import { deleteTemplate } from 'pages/templates/api/deleteTemplate.ts';

export default function useRemoveTemplate(props: { _id: string }) {
  const queryClient = useQueryClient();
  const { refetch: templateDelete, loading: templateLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['delete-template', {...props}],
      queryFn: () => deleteTemplate(props),
      onError: showError,
      onSuccess: () => queryClient.invalidateQueries(['all-templates']).finally(),
      enabled: false,
    }),
    transform: (data) => data,
  });
  return { templateDelete, templateLoading };
}