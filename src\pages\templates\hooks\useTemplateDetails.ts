import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { getTemplateDetails } from 'pages/templates/api/getTemplateDetails.ts';

export default function useTemplateDetails(props: { _id: string }) {
  const { data: templateDetails, loading } = useQueryFix({
    query: useQuery({
      queryKey: ['templates-details', props._id],
      queryFn: () => getTemplateDetails(props),
      onError: showError,
      refetchOnWindowFocus: false,
    }),
    transform: (response) => response.data,
  });
  return { templateDetails, loading };
}
