import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { fontFamily } from 'theme/typography.ts';
import Box from '@mui/material/Box';
import { InputBase } from '@mui/material';
import Paper from '@mui/material/Paper';
import IconButton from '@mui/material/IconButton';
import SearchIcon from '@mui/icons-material/Search';
import { TableComponent } from 'components/table/table.tsx';
import { useEffect, useMemo, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import BasicDropdown from 'components/dropdown.tsx';
import useShopRequest from 'pages/shop/hooks/useShopRequest.ts';
import CircularProgress from '@mui/material/CircularProgress';
import { ShopDto } from 'pages/shop/types/shop.ts';
import Avatar from '@mui/material/Avatar';
import useShopUpdate from 'pages/shop/hooks/useShopUpdate.ts';

export default function ShopRequest() {
  const [searchValue, setSearchValue] = useState('');
  const [requestToUpdate, setRequestToUpdate] = useState({ _id: '', isApproved: '' });
  const { shopRequests, shopLoading } = useShopRequest();
  const { updateRefetch } = useShopUpdate(requestToUpdate);

  useEffect(() => {
    if (!requestToUpdate._id) return;

    updateRefetch().finally(() => setRequestToUpdate({ _id: '', isApproved: '' }));
  }, [requestToUpdate]);

  const columns = useMemo<ColumnDef<ShopDto>[]>(
    () => [
      {
        header: 'User',
        accessorFn: (row) => row.seller.name,
        id: 'user',
        cell: (info) => {
          const seller = info.row.original.seller;
          const address = seller.address?.[0] || {};
          const profilePic = seller.name.charAt(0).toUpperCase();

          return (
            <Box>
              <Avatar>{profilePic}</Avatar>
              <Stack spacing={1} direction="column">
                <Typography variant="body1">
                  Username: <strong>{seller.name}</strong>
                </Typography>
                <Typography variant="body1">
                  Shop Name: <strong>{info.row.original.shopName}</strong>
                </Typography>
                <Typography variant="body1">
                  Email: <strong>{seller.email}</strong>
                </Typography>
                <Typography variant="body1">
                  Location:{' '}
                  <strong>
                    {address.street}, {address.city}, {address.state}, {address.country} -{' '}
                    {address.zip}
                  </strong>
                </Typography>
              </Stack>
            </Box>
          );
        },
        enableGlobalFilter: true,
      },
      {
        header: 'Shop Description',
        accessorFn: (row) => row.shopDescription,
        id: 'language',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Membership Plan',
        accessorFn: (row) => row.tier,
        id: 'name',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
        {
        header: 'Status',
        accessorFn: (row) => row.isApproved.toUpperCase(),
        id: 'isApproved',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Actions',
        accessorFn: () => {},
        id: 'actions',
        cell: (info) => {
          const options = [
            {
              title: 'Approve',
              action: () =>
                setRequestToUpdate({ _id: info.row.original._id, isApproved: 'approved' }),
            },
            {
              title: 'Decline',
              action: () =>
                setRequestToUpdate({ _id: info.row.original._id, isApproved: 'rejected' }),
            },
          ];
          return <BasicDropdown options={options} />;
        },
        enableGlobalFilter: false,
      },
    ],
    [],
  );

  if (shopLoading || !shopRequests) return <CircularProgress />;

  return (
    <>
      <Stack direction="row" gap={2} justifyContent="space-between" alignItems="end">
        <Typography
          variant="h5"
          fontWeight={600}
          letterSpacing={1}
          fontFamily={fontFamily.workSans}
          display={{ xs: 'none', lg: 'block' }}
        >
          Shop Requests
        </Typography>

        <Box>
          <Paper
            component="form"
            sx={{
              p: '2px 4px',
              display: 'flex',
              alignItems: 'end',
              justifyContent: 'center',
              width: { xs: '100%', sm: 300 },
            }}
          >
            <InputBase
              sx={{ ml: 1, flex: 1, border: 'none' }}
              placeholder="Search here"
              inputProps={{ 'aria-label': 'search' }}
              onChange={(e) => setSearchValue(e.target.value.trim())}
            />
            <IconButton type="button" sx={{ p: '10px' }} aria-label="search">
              <SearchIcon />
            </IconButton>
          </Paper>
        </Box>
      </Stack>

      <Box sx={{ overflowX: 'auto', width: '100%' }}>
        <TableComponent
          columns={columns}
          data={shopRequests}
          globalFilter={searchValue}
          setGlobalFilter={setSearchValue}
        />
      </Box>
    </>
  );
}