import Paper from '@mui/material/Paper';
import Grid from '@mui/material/Grid';
import { fontFamily } from 'theme/typography.ts';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Stack from "@mui/material/Stack";
import Button from "@mui/material/Button";
import FileDrop from "components/FileDrop.tsx";
import BasicModal from "components/modals/BasicModal.tsx";
import * as React from "react";
import {useNavigate} from "react-router-dom";

export default function CategoryUpload() {

    const navigate = useNavigate();
    const [open, setOpen] = React.useState(false);

    return (
        <>
            <Grid container spacing={{ xs: 2.5, sm: 3, lg: 3.75 }}>
                <Grid item xs={12} xl={8}>
                    <Paper elevation={3}>
                        <Stack direction={{xs: "column", sm: "row"}} justifyContent="space-between" alignItems="center" gap={2}>
                            <Box>
                                <Typography variant="subtitle1">
                                    Bulk Category Upload
                                </Typography>
                                <Typography variant="subtitle2" color="text.secondary" fontFamily={fontFamily.workSans}>
                                    You can add your categories with a CSV file from this section
                                </Typography>
                            </Box>
                            <Button
                                onClick={()=> navigate(`/category`)}
                                variant="contained" size="small" sx={{
                                width: {
                                    xs: '100%',
                                    sm: 'auto',
                                },
                            }}>
                                Categories
                            </Button>
                        </Stack>

                        <Box>
                            <Typography variant="subtitle2" color="text.secondary" mt={3} mb={1} fontFamily={fontFamily.workSans}>
                                CSV File
                            </Typography>
                            <FileDrop />
                        </Box>
                    </Paper>
                </Grid>

                <Grid item xs={12} xl={4}>
                    <Paper elevation={3}>
                        <Box>
                            <Typography variant="subtitle1">
                                Help Documents
                            </Typography>
                            <Typography variant="subtitle2" color="text.secondary" fontFamily={fontFamily.workSans}>
                                You can use these documents to generate your CSV file
                            </Typography>
                        </Box>
                        <Stack direction="column" spacing={1} mt={3}>
                            <Button type="button" variant="contained" size="medium" fullWidth disabled={true}>
                                Download CSV Template
                            </Button>
                            <Button type="button" variant="contained" size="medium" fullWidth disabled={true}>
                                Download CSV Example
                            </Button>
                            <Button type="button" variant="contained" size="medium" fullWidth onClick={()=> setOpen(true)}>
                                Documentation
                            </Button>
                        </Stack>
                    </Paper>
                </Grid>
            </Grid>
            <BasicModal open={open} setOpen={setOpen} />
        </>
    )
}