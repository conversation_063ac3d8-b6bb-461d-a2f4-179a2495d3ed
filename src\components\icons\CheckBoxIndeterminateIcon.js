import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { SvgIcon } from '@mui/material';
const CheckBoxIndeterminateIcon = (props) => {
    return (_jsxs(SvgIcon, { ...props, viewBox: "0 0 13 12", fill: "none", children: [_jsx("rect", { x: "1.26875", y: "0.679883", width: "11", height: "11", rx: "1.7", fill: "" }), _jsx("rect", { x: "1.26875", y: "0.679883", width: "11", height: "11", rx: "1.7", stroke: "", strokeWidth: "0.6" }), _jsx("g", { clipPath: "url(#clip0_4939_57759)", children: _jsx("path", { d: "M4.66895 6.0957H8.86895", stroke: "white", strokeLinecap: "round", strokeLinejoin: "round" }) }), _jsx("defs", { children: _jsx("clipPath", { id: "clip0_4939_57759", children: _jsx("rect", { width: "5.6", height: "5.6", fill: "white", transform: "translate(3.96875 3.37988)" }) }) })] }));
};
export default CheckBoxIndeterminateIcon;
