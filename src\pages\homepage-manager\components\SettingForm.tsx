import Stack from '@mui/material/Stack';
import { FormControl, InputLabel, Radio, RadioGroup } from '@mui/material';
import FormControlLabel from '@mui/material/FormControlLabel';
import Box from '@mui/material/Box';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import useSettingForm from 'pages/homepage-manager/hook/useSettingForm.ts';
import useSetting from "pages/homepage-manager/hook/useSetting.ts";
import CircularProgress from "@mui/material/CircularProgress";

const homeSettings = [
    {
        title: 'Featured Categories',
        _id: 'topOffers',
        options: [
            {
                title: 'Show',
                value: true,
            },
            {
                title: 'Hide',
                value: false,
            },
        ],
    },
    {
        title: 'Featured Products',
        _id: 'softwares',
        options: [
            {
                title: 'Show',
                value: true,
            },
            {
                title: 'Hide',
                value: false,
            },
        ],
    },
    {
        title: 'Latest Products',
        _id: 'newArrivals',
        options: [
            {
                title: 'Show',
                value: true,
            },
            {
                title: 'Hide',
                value: false,
            },
        ],
    },
    {
        title: 'Blog Slider',
        _id: 'articles',
        options: [
            {
                title: 'Show',
                value: true,
            },
            {
                title: 'Hide',
                value: false,
            },
        ],
    },
];


export default function SettingForm(){

    const {settings, settingLoading} = useSetting();
    const {formik} = useSettingForm({settings});

    if(!settings || settingLoading){
        return <CircularProgress />;
    }

    return (
        <form onSubmit={formik.handleSubmit}>
            {homeSettings.map(item=> (
                <Stack direction="row" alignItems="center" key={item._id}>
                    <InputLabel
                        component="label"
                        sx={{ fontSize: '12px', marginBottom: '20px', width: '50%' }}
                        size="small"
                        htmlFor="Visibility"
                    >
                        {item.title}
                    </InputLabel>
                    <FormControl>
                        <RadioGroup
                            row
                            aria-labelledby="demo-row-radio-buttons-group-label"
                            {...formik.getFieldProps(`${item._id}.enabled`)}
                            onChange={(e) => formik.setFieldValue(`${item._id}.enabled`, e.target.value)}
                        >
                            {item.options.map(opt=> <FormControlLabel value={opt.value} control={<Radio />} label={opt.title} />)}
                        </RadioGroup>
                    </FormControl>
                </Stack>
            ))}

            <Stack direction="row" spacing={2}>
                <Box width="50%">
                    <InputLabel
                        component="label"
                        sx={{ fontSize: '12px', marginBottom: '20px' }}
                        size="small"
                        htmlFor="topOffers.limit"
                    >
                        Top Offers Limit
                    </InputLabel>
                    <TextField
                        id=""
                        placeholder="10"
                        type="number"
                        fullWidth
                        sx={{ marginBottom: 2 }}
                        error={!!formik.errors.topOffers?.limit && formik.touched.topOffers?.limit}
                        helperText={
                            formik.errors.topOffers?.limit && formik.touched.topOffers?.limit
                                ? formik.errors.topOffers.limit
                                : ''
                        }
                        {...formik.getFieldProps('topOffers.limit')}
                    />
                </Box>

                <Box width="50%">
                    <InputLabel
                        component="label"
                        sx={{ fontSize: '12px', marginBottom: '20px' }}
                        size="small"
                        htmlFor="softwares.limit"
                    >
                        Softwares Limit
                    </InputLabel>
                    <TextField
                        placeholder="10"
                        id="softwares.limit"
                        type="number"
                        fullWidth
                        sx={{ marginBottom: 2 }}
                        error={!!formik.errors.softwares?.limit && formik.touched.softwares?.limit}
                        helperText={
                            formik.errors.softwares?.limit && formik.touched.softwares?.limit
                                ? formik.errors.softwares.limit
                                : ''
                        }
                        {...formik.getFieldProps('softwares.limit')}
                    />
                </Box>
            </Stack>
            <Stack direction="row" spacing={2}>
                <Box width="50%">
                    <InputLabel
                        component="label"
                        sx={{ fontSize: '12px', marginBottom: '20px' }}
                        size="small"
                        htmlFor="newArrivals.limit"
                    >
                        New Arrival Limit
                    </InputLabel>
                    <TextField
                        placeholder="10"
                        id="newArrivals.limit"
                        type="number"
                        fullWidth
                        sx={{ marginBottom: 2 }}
                        error={!!formik.errors.newArrivals?.limit && formik.touched.newArrivals?.limit}
                        helperText={
                            formik.errors.newArrivals?.limit && formik.touched.newArrivals?.limit
                                ? formik.errors.newArrivals.limit
                                : ''
                        }
                        {...formik.getFieldProps('newArrivals.limit')}
                    />
                </Box>
                <Box width="50%">
                    <InputLabel
                        component="label"
                        sx={{ fontSize: '12px', marginBottom: '20px' }}
                        size="small"
                        htmlFor="articles.limit"
                    >
                        Articles Limit
                    </InputLabel>
                    <TextField
                        placeholder="10"
                        id="articles.limit"
                        type="number"
                        fullWidth
                        sx={{ marginBottom: 2 }}
                        error={!!formik.errors.articles?.limit && formik.touched.articles?.limit}
                        helperText={
                            formik.errors.articles?.limit && formik.touched.articles?.limit
                                ? formik.errors.articles.limit
                                : ''
                        }
                        {...formik.getFieldProps('articles.limit')}
                    />
                </Box>
            </Stack>
            <Button
                type="submit"
                variant="contained"
                size="medium"
                fullWidth
                disabled={formik.isSubmitting}
            >
                {formik.isSubmitting ? "Processing..." : "Submit"}
            </Button>
        </form>
    )
}