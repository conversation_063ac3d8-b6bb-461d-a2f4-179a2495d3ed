import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { fontFamily } from 'theme/typography';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import iPhone from 'assets/images/iPhone.png';
import AWS8 from 'assets/images/AWS8.png';
import Product from './Product';
const productsData = [
    {
        id: 1,
        name: 'iPhone 14 Pro Max',
        imageUrl: iPhone,
        inStock: 524,
        price: '1,099.00',
    },
    {
        id: 2,
        name: 'Apple Watch S8',
        imageUrl: AWS8,
        inStock: 320,
        price: '799.00',
    },
];
const Products = () => {
    return (_jsxs(Stack, { direction: "column", gap: 3.75, component: Paper, height: 300, children: [_jsx(Typography, { variant: "h6", fontWeight: 400, fontFamily: fontFamily.workSans, children: "Products" }), _jsxs(Stack, { justifyContent: "space-between", children: [_jsx(Typography, { variant: "caption", fontWeight: 400, children: "Products" }), _jsx(Typography, { variant: "caption", fontWeight: 400, children: "Price" })] }), productsData.map((item) => {
                return _jsx(Product, { data: item }, item.id);
            })] }));
};
export default Products;
