import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import React, { useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Box, CircularProgress, Paper, Typography } from '@mui/material';
import IconifyIcon from 'components/base/IconifyIcon.tsx';
import bulkUpload from 'pages/category/api/bulkUpload.ts';
const FileDrop = () => {
    const [isLoading, setIsLoading] = React.useState(false);
    const onDrop = useCallback((acceptedFile) => {
        setIsLoading(true);
        uploadFile(acceptedFile[0]).finally(() => setIsLoading(false));
    }, []);
    const { getRootProps, getInputProps } = useDropzone({
        onDrop,
        accept: {
            'text/csv': [],
            'application/vnd.ms-excel': [],
        },
        maxFiles: 1,
        maxSize: 10485760,
    });
    const uploadFile = async (acceptedFile) => {
        const formData = new FormData();
        formData.append('csvFile', acceptedFile);
        await bulkUpload(formData);
    };
    return (_jsxs(Box, { sx: { width: '100%' }, children: [_jsxs(Paper, { ...getRootProps(), sx: {
                    border: '2px dashed #2196f3',
                    padding: 12,
                    textAlign: 'center',
                    cursor: 'pointer',
                }, children: [_jsx("input", { ...getInputProps() }), _jsx(IconifyIcon, { icon: "ion:cloud-upload-outline", sx: { fontSize: 48 } }), _jsx(Typography, { variant: "h6", color: "textSecondary", children: "Drag & drop files here, or click to select" })] }), isLoading && (_jsx(Box, { sx: { display: 'flex', justifyContent: 'center', marginTop: 3 }, children: _jsx(CircularProgress, {}) }))] }));
};
export default FileDrop;
