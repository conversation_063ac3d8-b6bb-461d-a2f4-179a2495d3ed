export interface BlogPost {
    _id: string;
    title: string;
    summary: string;
    metaTags: string[];
    keywords: string[];
    language: string;
    image?:string;
    tags: string[];
    category: {
        _id: string;
        language: string;
        categoryName: string;
        description: string;
        keywords: string[];
        order: number;
        createdAt: string; // ISO date string
        updatedAt: string; // ISO date string
        slug: string;
        __v: number;
    };
    details: {
        description: string;
        germanDetails: {
            description: string;
            image?:string;
            keywords: string[];
            title: string;
            tags: string[];
            summary: string;
        };
        frenchDetails: {
            description: string;
            image?:string;
            keywords: string[];
            title: string;
            tags: string[];
            summary: string;
        };
    };
    createdAt: string;
    updatedAt: string;
    slug: string;
    __v: number;
}