import { BrowserRouter } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "react-query";
import BasicRouter from "routes/router.tsx";
import {MessageListener} from "listener/MessageListener.ts";
import {getAccessToken} from "vbrae-utils";

const App = () => {

  const queryClient = new QueryClient();
  const hasUser = !!getAccessToken();

  return (
      <BrowserRouter>
        <QueryClientProvider client={queryClient}>
            {hasUser && <MessageListener />}
          <BasicRouter />
        </QueryClientProvider>
      </BrowserRouter>
  )
};

export default App;
