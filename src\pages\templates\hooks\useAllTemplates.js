import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { getAllTemplates } from 'pages/templates/api/getAllTemplates.ts';
export default function useAllTemplates() {
    const { data, loading } = useQueryFix({
        query: useQuery({
            queryKey: 'all-templates',
            queryFn: () => getAllTemplates(),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data,
    });
    return { data, loading };
}
