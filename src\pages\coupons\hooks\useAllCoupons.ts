import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { getAllCoupons } from 'pages/coupons/api/getAllCoupons.ts';

export default function useAllCoupons({ query }: { query: string }) {
  const {
    data: coupons,
    loading: couponsLoading,
    refetch: couponsRefetch,
  } = useQueryFix({
    query: useQuery({
      queryKey: ['all-coupons', query],
      queryFn: () => getAllCoupons({ query }),
      onError: showError,
      enabled: false
    }),
    transform: (data) => data.data,
  });
  return { coupons, couponsLoading, couponsRefetch };
}
