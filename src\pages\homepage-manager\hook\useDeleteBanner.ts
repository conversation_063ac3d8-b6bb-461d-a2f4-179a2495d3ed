import { showError, useQueryFix } from 'vbrae-utils';
import {useQuery, useQueryClient} from 'react-query';
import { deleteBanner } from 'pages/homepage-manager/api/deleteBanner.ts';

export default function useDeleteBanner({ _id }: { _id?: string }) {
  const queryClient = useQueryClient();
  const { refetch: deleteRefetch, loading: deleteLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['banners-delete', _id],
      queryFn: () => deleteBanner({ _id: _id! }),
      onError: showError,
      enabled: !!_id,
        onSuccess: ()=> queryClient.invalidateQueries('all-banners').finally()
    }),
    transform: (data) => data,
  });
  return { deleteRefetch, deleteLoading };
}
