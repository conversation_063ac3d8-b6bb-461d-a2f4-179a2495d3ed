import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { fontFamily } from 'theme/typography';
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import ButtonBase from '@mui/material/ButtonBase';
const VisitorsChartLegend = ({ data, toggleColor, handleToggleLegend }) => {
    let color = '';
    if (toggleColor.organic && data.type === 'Organic') {
        color = 'primary.main';
    }
    else if (toggleColor.social && data.type === 'Social') {
        color = 'secondary.lighter';
    }
    else if (toggleColor.direct && data.type === 'Direct') {
        color = 'secondary.light';
    }
    else {
        color = 'text.secondary';
    }
    return (_jsxs(Stack, { alignItems: "center", justifyContent: "space-between", children: [_jsx(ButtonBase, { onClick: (e) => handleToggleLegend(e, data.type), disableRipple: true, children: _jsxs(Stack, { spacing: 1, alignItems: "center", children: [_jsx(Box, { height: 8, width: 8, bgcolor: color, borderRadius: 1 }), _jsx(Typography, { variant: "body1", color: "text.secondary", fontFamily: fontFamily.workSans, children: data.type })] }) }), _jsx(Typography, { variant: "body1", color: "text.primary", fontFamily: fontFamily.workSans, children: data.rate })] }));
};
export default VisitorsChartLegend;
