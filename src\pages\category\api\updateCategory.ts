import { patchRequest } from 'vbrae-utils';

interface CategoryFormValues {
    _id: string;
    categoryName: {
        en: string;
        fr: string;
        it: string;
        de: string;
        es: string;
    };
    metaTitle: string;
    metaDescription: string;
    metaKeywords: string;
    category_order: number;
    visibility: boolean;
    showInMainMenu: boolean;
    showImageOnMainMenu: boolean;
    image: File | null | string;
    parent_id: string | null | boolean;
};


export async function updateCategory(props: CategoryFormValues): Promise<{message: string} | undefined> {
    const r = await patchRequest<CategoryFormValues>({
        url: `sub-catagory/${props._id}`,
        data : props,
        useAuth: true
    });
    return r.response
}