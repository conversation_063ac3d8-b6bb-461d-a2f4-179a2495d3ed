import React, { useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Box, CircularProgress, Paper, Typography } from '@mui/material';
import IconifyIcon from 'components/base/IconifyIcon.tsx';
import bulkUpload from 'pages/category/api/bulkUpload.ts';
import {showError, showSuccess} from "vbrae-utils";

const FileDrop = () => {
  const [isLoading, setIsLoading] = React.useState(false);

  const onDrop = useCallback((acceptedFile: File[]) => {
    setIsLoading(true);
    uploadFile(acceptedFile[0]).finally(() => setIsLoading(false));
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      'text/csv': [],
      'application/vnd.ms-excel': [],
    },
    maxFiles: 1,
    maxSize: 10485760,
  });

  const uploadFile = async (acceptedFile: File) => {
    const formData = new FormData();
    formData.append('csvFile', acceptedFile);

    try {
        await bulkUpload(formData);
        showSuccess("File uploaded successfully.");
    }
    catch (e){
        showError({message: "Something went wrong, please try again."});
    }
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Paper
        {...getRootProps()}
        sx={{
          border: '2px dashed #2196f3',
          padding: {xs:4, sm: 12},
          textAlign: 'center',
          cursor: 'pointer',
        }}
      >
        <input {...getInputProps()} />
        <IconifyIcon icon="ion:cloud-upload-outline" sx={{ fontSize: 48 }} />
        <Typography variant="h6" color="textSecondary">
          Drag & drop files here, or click to select
        </Typography>
      </Paper>

      {isLoading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', marginTop: 3 }}>
          <CircularProgress />
        </Box>
      )}
    </Box>
  );
};

export default FileDrop;
