import { useMemo, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import BasicDropdown from 'components/dropdown.tsx';
import CircularProgress from '@mui/material/CircularProgress';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { fontFamily } from 'theme/typography.ts';
import Box from '@mui/material/Box';
import { InputBase } from '@mui/material';
import Paper from '@mui/material/Paper';
import IconButton from '@mui/material/IconButton';
import { TableComponent } from 'components/table/table.tsx';
import SearchIcon from '@mui/icons-material/Search';
import Button from '@mui/material/Button';
import useTaxList from 'pages/tax/hooks/useTaxList.ts';
import { EUTax } from 'pages/tax/types/tax.ts';
import useResetTax from 'pages/tax/hooks/useResetTax.ts';
import TaxModal from 'pages/tax/components/TaxModal.tsx';

const initialState = {
  isOpen: false,
  state: {
    _id: '',
    country: '',
    taxPercentage: '0',
  },
};

export default function TaxView() {
  const [searchValue, setSearchValue] = useState('');
  const { taxList } = useTaxList();
  const [taxToUpdate, setTaxToUpdate] = useState(initialState);
  const { resetLoading, resetRefetch } = useResetTax();

  const columns = useMemo<ColumnDef<EUTax>[]>(
    () => [
      {
        header: 'Country',
        accessorFn: (row) => row.country,
        id: 'country',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Tax Percentage',
        accessorFn: (row) => row.taxPercentage,
        id: 'content',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Actions',
        accessorFn: () => {},
        id: 'actions',
        cell: (info) => {
          const options = [
            {
              title: 'Edit',
              action: () => setTaxToUpdate({ isOpen: true, state: info.row.original }),
            },
          ];
          return <BasicDropdown options={options} />;
        },
        enableGlobalFilter: false,
      },
    ],
    [],
  );

  if (!taxList) return <CircularProgress />;

  return (
    <>
      <TaxModal
        open={taxToUpdate.isOpen}
        state={taxToUpdate.state}
        handleClose={() => setTaxToUpdate(initialState)}
      />
      <Stack direction="column" spacing={2}>
        <Typography
          variant="h5"
          fontWeight={600}
          letterSpacing={1}
          fontFamily={fontFamily.workSans}
          display={{ xs: 'none', lg: 'block' }}
        >
          Tax List
        </Typography>
      </Stack>

      <Stack direction="row" gap={2} justifyContent="space-between" alignItems="end">
        <Button type="submit" variant="contained" size="small" onClick={resetRefetch} disabled={resetLoading}>
          Reset Taxes
        </Button>
        <Box>
          <Paper
            component="form"
            sx={{
              p: '2px 4px',
              display: 'flex',
              alignItems: 'end',
              justifyContent: 'center',
              width: { xs: '100%', sm: 300 },
            }}
          >
            <InputBase
              sx={{ ml: 1, flex: 1, border: 'none' }}
              placeholder="Search here"
              inputProps={{ 'aria-label': 'search' }}
              onChange={(e) => setSearchValue(e.target.value.trim())}
            />
            <IconButton type="button" sx={{ p: '10px' }} aria-label="search">
              <SearchIcon />
            </IconButton>
          </Paper>
        </Box>
      </Stack>

      <Box sx={{ overflowX: 'auto', width: '100%' }}>
        <TableComponent
          columns={columns}
          data={taxList}
          globalFilter={searchValue}
          setGlobalFilter={setSearchValue}
        />
      </Box>
    </>
  );
}