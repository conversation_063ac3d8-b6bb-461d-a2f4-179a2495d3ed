import React, { ErrorInfo } from 'react';
import { Button, Box, Typography, Container } from '@mui/material';

interface State {
    hasError: boolean;
}

interface ErrorBoundaryProps {
    children: React.ReactNode;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, State> {
    state: State = { hasError: false };

    static getDerivedStateFromError(error: Error): State {
        console.error(error);
        return { hasError: true };
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
        console.error(error, errorInfo); // Log error details
    }

    render() {
        if (this.state.hasError) {
            return (
                <Container maxWidth="sm">
                    <Box
                        sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            height: '100vh',
                            textAlign: 'center',
                            backgroundColor: 'rgba(0, 0, 0, 0.1)',
                            borderRadius: 2,
                            boxShadow: 3,
                            padding: 3,
                        }}
                    >
                        <Typography variant="h4" component="div" color="error" sx={{ fontWeight: 'bold', marginBottom: 2 }}>
                            Something went wrong!
                        </Typography>
                        <Typography variant="body1" sx={{ marginBottom: 3 }}>
                            Oops! There was an error.<br />
                            Our team has been notified, and we are working on a resolution for you!
                        </Typography>
                        <Button
                            variant="contained"
                            color="primary"
                            onClick={() => {
                                this.setState({ hasError: false });
                                window.location.href = '/';
                            }}
                        >
                            Back to Home
                        </Button>
                    </Box>
                </Container>
            );
        } else {
            return this.props.children;
        }
    }
}

export default ErrorBoundary;
