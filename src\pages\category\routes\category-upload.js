import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import Paper from '@mui/material/Paper';
import Grid from '@mui/material/Grid';
import { fontFamily } from 'theme/typography.ts';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Stack from "@mui/material/Stack";
import Button from "@mui/material/Button";
import FileDrop from "components/FileDrop.tsx";
import BasicModal from "components/modals/BasicModal.tsx";
import * as React from "react";
export default function CategoryUpload() {
    const [open, setOpen] = React.useState(false);
    return (_jsxs(_Fragment, { children: [_jsxs(Grid, { container: true, spacing: { xs: 2.5, sm: 3, lg: 3.75 }, children: [_jsx(Grid, { item: true, xs: 12, xl: 8, children: _jsxs(Paper, { elevation: 3, children: [_jsxs(Stack, { direction: "row", justifyContent: "space-between", alignItems: "center", children: [_jsxs(Box, { children: [_jsx(Typography, { variant: "subtitle1", children: "Bulk Category Upload" }), _jsx(Typography, { variant: "subtitle2", color: "text.secondary", fontFamily: fontFamily.workSans, children: "You can add your categories with a CSV file from this section" })] }), _jsx(Button, { type: "submit", variant: "contained", size: "small", children: "Categories" })] }), _jsxs(Box, { children: [_jsx(Typography, { variant: "subtitle2", color: "text.secondary", mt: 3, mb: 1, fontFamily: fontFamily.workSans, children: "CSV File" }), _jsx(FileDrop, {})] })] }) }), _jsx(Grid, { item: true, xs: 12, xl: 4 })] }), _jsx(BasicModal, { open: open, setOpen: setOpen })] }));
}
