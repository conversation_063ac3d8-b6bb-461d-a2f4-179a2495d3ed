import { jsx as _jsx } from "react/jsx-runtime";
import { Link as RouterLink } from 'react-router-dom';
import { forwardRef } from 'react';
const LinkBehavior = forwardRef((props, ref) => {
    const { href, ...other } = props;
    // Map href (Material UI) -> to (react-router)
    return _jsx(RouterLink, { ref: ref, to: href, ...other });
});
const Link = {
    defaultProps: {
        underline: 'none',
        component: LinkBehavior,
    },
};
export default Link;
