import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import getGiveAwayDetails from 'pages/give-away/api/getGiveAwayDetails.ts';

export type PropsDto = {
  _id: string;
};

export default function useGiveAwayDetails(props:PropsDto) {
  const { data: details, loading: detailsLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['giveAway-details', {...props}],
      queryFn: () => getGiveAwayDetails(props),
      onError: showError,
    }),
    transform: (response) => response.data,
  });
  return { details, detailsLoading };
}
