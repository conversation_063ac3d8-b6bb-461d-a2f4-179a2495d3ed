import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Navigate, Route, Routes } from 'react-router-dom';
import { Suspense } from 'react';
import Splash from 'components/loading/Splash.tsx';
import MainLayout from 'layouts/main-layout';
import Dashboard from 'pages/dashboard';
import AuthLayout from 'layouts/auth-layout';
import Login from 'pages/authentication/Login.tsx';
import CategoryAdd from 'pages/category/routes/category-add.tsx';
import CategoryView from 'pages/category/routes/category-view.tsx';
import CategoryUpload from 'pages/category/routes/category-upload.tsx';
import TemplateList from 'pages/templates/routes/template-list.tsx';
import TemplateIgdb from 'pages/templates/routes/template-igdb.tsx';
import TemplateAdd from 'pages/templates/routes/template-add.tsx';
import TemplateRequest from 'pages/templates/routes/template-request.tsx';
import CategoryEdit from 'pages/category/routes/category-edit.tsx';
import TemplateEdit from 'pages/templates/routes/template-edit.tsx';
const BasicRouter = () => {
    return (_jsx(Suspense, { fallback: _jsx(Splash, {}), children: _jsxs(Routes, { children: [_jsx(Route, { path: "auth", element: _jsx(AuthLayout, {}), children: _jsx(Route, { path: "login", element: _jsx(Login, {}) }) }), _jsxs(Route, { path: "/", element: _jsx(MainLayout, {}), children: [_jsx(Route, { index: true, element: _jsx(Dashboard, {}) }), _jsx(Route, { path: "/category", element: _jsx(CategoryView, {}) }), _jsx(Route, { path: "/category/add", element: _jsx(CategoryAdd, {}) }), _jsx(Route, { path: "/category/upload", element: _jsx(CategoryUpload, {}) }), _jsx(Route, { path: "/category/:id", element: _jsx(CategoryEdit, {}) }), _jsx(Route, { path: "/templates/list", element: _jsx(TemplateList, {}) }), _jsx(Route, { path: "/templates/igdb", element: _jsx(TemplateIgdb, {}) }), _jsx(Route, { path: "/templates/add", element: _jsx(TemplateAdd, {}) }), _jsx(Route, { path: "/templates/:id", element: _jsx(TemplateEdit, {}) }), _jsx(Route, { path: "/templates/request", element: _jsx(TemplateRequest, {}) })] }), _jsx(Route, { path: "/404", element: _jsx("p", { children: "Not Found" }) }), _jsx(Route, { path: "*", element: _jsx(Navigate, { to: '/404' }) })] }) }));
};
export default BasicRouter;
