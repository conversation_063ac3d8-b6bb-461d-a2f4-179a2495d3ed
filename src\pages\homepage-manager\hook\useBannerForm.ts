import * as Yup from 'yup';
import { useMutation, useQueryClient } from 'react-query';
import { useFormik } from 'formik';
import { showError, showSuccess } from 'vbrae-utils';
import { SelectedValue } from 'components/select-menu.tsx';
import { postBannerForm } from 'pages/homepage-manager/api/postBannerForm.ts';
import { uploadUrl } from 'pages/category/api/uploadUrl.ts';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { BannerDto, KeyValues } from 'pages/homepage-manager/types/homepage-manager.ts';
import { patchBannerForm } from 'pages/homepage-manager/api/patchBannerForm.ts';
import { categoryOptions } from 'pages/homepage-manager/const/category-options.ts';

export type ErrorType = {
  message: string;
};

const fileOrUrlSchema = Yup.mixed()
    .test("fileOrUrl", "Image is required", (value) => {
        return (
            value instanceof File ||
            (typeof value === "string" && value.trim() !== "")
        );
    });

const bannerSchema = Yup.object().shape({
    link:Yup.string().trim(),
    title:Yup.string().trim(),
    shortSummary:Yup.string().trim(),
    discountPercent:Yup.number(),
    couponCode:Yup.string().trim(),
    existingPrice:Yup.number(),
    order: Yup.number(),
    tag: Yup.string().trim(),
    category: Yup.object().shape({
        _id: Yup.string().min(1, 'category is required').required("category is required"),
        name: Yup.string(),
    }),
    logoImage: Yup.mixed().nullable(),
    images: Yup.object().shape({
        desktop: fileOrUrlSchema.required("Desktop image is required")
    })
});

type InitialValuesDto = {
    link: string;
    title: string;
    shortSummary: string;
    discountPercent: number;
    existingPrice: number;
    couponCode: string;
    order: number;
    tag: string;
    category: SelectedValue;
    images: {
        desktop: File | string | null,
    };
    logoImage: string | null,
    startTime: string;
    endTime: string;
};

type BannerProps = {
    setOpen: Dispatch<SetStateAction<KeyValues>>;
    bannerDetails?: BannerDto;
    _id?: string;
}

export default function useBannerForm({ setOpen, bannerDetails, _id }: BannerProps) {

    const queryClient = useQueryClient();
    const startTime = new Date();
    const endTime = new Date();
    endTime.setDate(endTime.getDate() + 1);

    const [initialValues, setInitialValues] = useState<InitialValuesDto>({
        link: '',
        title: '',
        tag: '',
        shortSummary: '',
        discountPercent: 0,
        existingPrice: 0,
        couponCode: '',
        order: 1,
        category: categoryOptions[1],
        images: {
            desktop: null,
        },
        logoImage: null,
        startTime: String(startTime),
        endTime: String(endTime),
    })

    useEffect(() => {
        if(!bannerDetails || !_id) {
            setInitialValues({
                link: '',
                title: '',
                tag: '',
                shortSummary: '',
                discountPercent: 0,
                existingPrice: 0,
                couponCode: '',
                order: 1,
                category: categoryOptions[1],
                images: {
                    desktop: null,
                },
                logoImage: null,
                startTime: String(startTime),
                endTime: String(endTime),
            });
            return;
        };

        const categoryTitle = categoryOptions.filter(item=> item._id === bannerDetails.category)[0]

        setInitialValues({
            link: bannerDetails.link,
            title: bannerDetails.title,
            tag: bannerDetails.tag,
            shortSummary: bannerDetails.shortSummary,
            discountPercent: bannerDetails.discountPercent,
            existingPrice: bannerDetails.existingPrice,
            couponCode: bannerDetails.couponCode,
            order: bannerDetails.order,
            logoImage: bannerDetails.logoImage || null,
            category: categoryTitle,
            images: bannerDetails.images,
            startTime: bannerDetails.startTime ?? '',
            endTime: bannerDetails.endTime ?? '',
        })
    }, [_id, bannerDetails]);

    const { mutateAsync } = useMutation(postBannerForm, {
        onError: (error:ErrorType)=>showError(error),
    });

    const { mutateAsync : updateAsync } = useMutation(patchBannerForm, {
        onError: (error:ErrorType)=>showError(error),
    });

    const uploadFile = async (file: File): Promise<string> => {
        const fileType = file.name.split('.').pop()?.toLowerCase() || "unknown";
        const { url: resignedUrl, path: filePath } = await uploadUrl({ name: file.name, fileType });

        await fetch(resignedUrl, {
            method: "PUT",
            headers: { "Content-Type": file.type, "x-amz-acl": "public-read" },
            body: file,
        });

        return filePath;
    };

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: bannerSchema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);

            const imageUrls = { ...bannerDetails?.images };
            let logoImage = bannerDetails?.logoImage;
            const file = values.images["desktop"];
            if (file instanceof File) {
                imageUrls["desktop"] = await uploadFile(file);
            }
            // @ts-ignore
            if(values.logoImage instanceof File) {
                logoImage = await uploadFile(values.logoImage);
            }

            let response
            if(_id && bannerDetails) response = await updateAsync({ ...values, images: imageUrls!, location: values.category._id as string, _id: bannerDetails._id, logoImage });
            else response = await mutateAsync({ ...values, images: imageUrls!, location: values.category._id as string, logoImage });
            setSubmitting(false);
            resetForm();
            if (response) {
                setOpen({
                    _id: undefined,
                    open: false
                })
                queryClient.invalidateQueries('all-banners').finally();
                showSuccess("Banner added successfully.");
            }
        },
    });

    return { formik };
}
