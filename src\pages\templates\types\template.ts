import { CategoryDto } from 'pages/category/types/category.ts';

interface SeoDetails {
  metaTitle: string;
  metaDescription: string;
  metaKeywords: string;
}

interface LanguageDetails {
    title: string;
    description: string;
    seoKeywords: string;
}

export interface TemplateDetails {
    seo: SeoDetails;
    germanDetails?: LanguageDetails;
    frenchDetails?: LanguageDetails;
    spanishDetails?: LanguageDetails;
    italianDetails?: LanguageDetails;
    title: string;
    description: string;
}

export interface TemplateDto {
    _id: string;
    details: TemplateDetails;
    coverImage: string;
    active: boolean;
    templateName: string;
    slug: string;
    listingType: string;
    category: CategoryDto | null;
    subcategory: CategoryDto | null;
    region: string;
    genres: string;
    releaseDate: string;
    preOrder: boolean;
    price: number;
    productType: string;
    serviceFee: null | string;
    dlc: boolean;
    specificCountrySellingOption: boolean;
    languages: string[];
    videos: string[];
    images: string[];
    createdAt: string;
}