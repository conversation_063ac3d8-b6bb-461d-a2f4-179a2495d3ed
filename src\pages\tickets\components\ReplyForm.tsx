import React, { useRef } from 'react';
import { Box, Chip, IconButton, InputAdornment, TextField, Tooltip } from '@mui/material';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import CloseIcon from '@mui/icons-material/Close';
import SendIcon from '@mui/icons-material/Send';
import { FilePreviewContainer, InputContainer } from 'pages/tickets/const/chat.ts';
import useMessageForm from '../hooks/useMessageForm';

export default function ReplyForm() {
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const { formik } = useMessageForm();

  const handleAttachClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newFiles = Array.from(e.target.files || []);
    const updatedFiles = [...(formik.values.attachments || []), ...newFiles];
    formik.setFieldValue('attachments', updatedFiles);
  };

  const handleRemoveFile = (index: number) => {
    const updated = (formik.values.attachments || []).filter((_, i) => i !== index);
    formik.setFieldValue('attachments', updated);
  };

  return (
    <form onSubmit={formik.handleSubmit}>
      <InputContainer>
        {formik.values.attachments?.length > 0 && (
          <FilePreviewContainer>
            {formik.values.attachments.map((_, index) => (
              <Chip
                key={index}
                label={`File# ${index + 1}`}
                onDelete={() => handleRemoveFile(index)}
                deleteIcon={<CloseIcon />}
                color="primary"
                variant="outlined"
              />
            ))}
          </FilePreviewContainer>
        )}
        <Box display="flex" gap={1} alignItems="flex-end">
          <TextField
            fullWidth
            multiline
            maxRows={4}
            placeholder="Type your message..."
            name="content"
            value={formik.values.content}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.content && Boolean(formik.errors.content)}
            helperText={formik.touched.content && formik.errors.content}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <Tooltip title="Attach file">
                    <IconButton size="small" onClick={handleAttachClick}>
                      <AttachFileIcon />
                    </IconButton>
                  </Tooltip>
                  <input
                    type="file"
                    multiple
                    hidden
                    ref={fileInputRef}
                    onChange={handleFileChange}
                  />
                </InputAdornment>
              ),
            }}
          />
          <Tooltip title="Send message">
            <IconButton
              type="submit"
              color="primary"
              disabled={
                formik.isSubmitting ||
                (!formik.values.content?.trim() &&
                  (!formik.values.attachments || formik.values.attachments.length === 0))
              }
              sx={{ backgroundColor: '#1E88E5', color: '#fff' }}
            >
              <SendIcon sx={{ color: '#fff' }} />
            </IconButton>
          </Tooltip>
        </Box>
      </InputContainer>
    </form>
  );
}
