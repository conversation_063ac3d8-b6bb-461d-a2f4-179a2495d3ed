import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import { FormControl, InputLabel, Radio, RadioGroup, SelectChangeEvent } from '@mui/material';
import SelectMenu, { SelectedValue } from 'components/select-menu.tsx';
import Box from '@mui/material/Box';
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import CustomAccordion from 'components/accordion/accordian.tsx';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import useCategories from 'pages/category/hooks/useCategories.ts';
import { languages } from 'pages/category/const/languages.ts';
import { generateAccordion } from 'pages/category/utils/misc.ts';
import { AccordionDto } from 'pages/category/types/accordion.ts';
import CircularProgress from '@mui/material/CircularProgress';
import useDeleteCategory from "pages/category/hooks/useDeleteCategory.ts";

const label = { inputProps: { 'aria-label': 'Checkbox demo' } };

export default function CategoryView(){

    const navigate = useNavigate();

    const [sortBy, setSortBy] = useState("all");
    const [order, setOrder] = useState("asc");
    const [categoryId, setCategoryId] = useState<string>('');

    const {categories, categoryLoading} = useCategories({sortBy, order});
    const { categoryDelete, categoryDelLoading } = useDeleteCategory({_id: categoryId});

    const [accordionData, setAccordionData] = useState<AccordionDto[]>([]);
    const [selectedCategory, setSelectedCategory] = useState<SelectedValue>({_id: '0', name: "All"})
    const [selectedLanguage, setSelectedLanguage] = useState<SelectedValue>(languages[0]);

    const handleChange = (event:SelectChangeEvent<string>, id: string) => {
        console.log(id)
        if(event.target.value.includes("_")){
            const [a, b] = event.target.value.split("_");
            setSortBy(a);
            setOrder(b);
            return;
        }
        setOrder("asc")
        setSortBy(event.target.value);
    };

    useEffect(() => {
        if(!categories) return;

        setAccordionData(generateAccordion(categories))
    }, [categories]);

    useEffect(() => {
        if(!categoryId) return;

        categoryDelete().finally(() => setCategoryId(''));
    }, [categoryId]);

    const handleSelectChange = (id : string, value?: SelectedValue) => {
        if(!value || !categories) return;

        if(id === 'parent_category') {
            if(value._id === '0') {
                setSelectedCategory({_id: '0', name: "All"});
                setAccordionData(generateAccordion(categories))
                return;
            }
            const filterData = categories.filter(item=> item._id === value._id);
            const accData = generateAccordion(filterData);
            setAccordionData(accData)
            value && setSelectedCategory(value);
        }
        else if(id === 'language') {
            value && setSelectedLanguage(value);
        }
    };

    return (
        <Stack spacing={2} direction="column">
            {categoryDelLoading && <CircularProgress />}
            <Paper elevation={3} sx={{ py: 4, width: '100%' }}>
                <Stack direction="row" alignItems="center" justifyContent="space-between" gap={2}>
                    <Typography variant="h5" fontWeight={600}>
                        Categories
                    </Typography>
                    <Button type="submit" variant="contained" size="small" onClick={()=> navigate(`/category/add`)}>
                        Add Category
                    </Button>
                </Stack>

                {categoryLoading || !categories ? <CircularProgress /> : <>
                    <Stack direction={{ xs: 'column', sm: 'row' }} alignItems="center" gap={{
                        xs: 0,
                        sm: 2
                    }}>
                        <Box width={{xs: "100%", sm:"200px"}}>
                            <InputLabel
                                component="label"
                                sx={{ fontSize: '12px', marginBottom: '20px' }}
                                size="small"
                                htmlFor="language"
                            >
                                Language
                            </InputLabel>
                            <SelectMenu
                                id="language"
                                value={selectedLanguage}
                                handleChange={handleSelectChange}
                                options={languages}
                            />
                        </Box>
                        <Box width={{xs: "100%", sm:"300px"}}>
                            <InputLabel
                                component="label"
                                sx={{ fontSize: '12px', marginBottom: '20px' }}
                                size="small"
                                htmlFor="parent_category"
                            >
                                Parent Category
                            </InputLabel>
                            <SelectMenu
                                value={selectedCategory}
                                id="parent_category"
                                handleChange={handleSelectChange}
                                options={[{_id: '0', name: "All"}, ...categories.map(item=>({
                                    _id: item._id,
                                    name: item.categoryName.en,
                                }))]}
                            />
                        </Box>
                    </Stack>

                    {categories?.length > 0 ? <CustomAccordion
                        data={accordionData}
                        onParentDelete={({_id})=> setCategoryId(_id)}
                        onChildDelete={({_id})=> setCategoryId(_id)}
                        onParentEdit={({ _id }) => navigate(`/category/${_id}`)}
                        onChildEdit={({ _id }) => navigate(`/category/${_id}`)}
                    /> : <Typography
                        variant="body1"
                        sx={{
                            fontWeight: 'bold',
                            fontSize: 16,
                            textAlign: 'center',
                            marginTop: 2
                        }}
                    >
                        Nothing here yet!
                    </Typography>}
                </>}

            </Paper>

            <Paper elevation={3} sx={{ py: 4 , width: {
                    xs: '100%',
                    lg: '50%',
                } }}>
                <Stack spacing={2} direction="column">
                    <Typography variant="h5" fontWeight={600}>
                        Settings
                    </Typography>

                    <Box>
                        <InputLabel
                            component="label"
                            sx={{ fontSize: '12px', marginBottom: '20px', width: "50%" }}
                            size="small"
                            htmlFor="sort_categories"
                        >
                            Sort Categories
                        </InputLabel>
                        <FormControl>
                            <RadioGroup
                                row
                                aria-labelledby="demo-row-radio-buttons-group-label"
                                onChange={(e)=>handleChange(e, 'sortBy')}
                            >
                                <FormControlLabel value="categoryOrder" control={<Radio />} label="By Category Order" />
                                <FormControlLabel value="date_asc" control={<Radio />} label="By Date" />
                                <FormControlLabel value="date_desc" control={<Radio />} label="By Date (Desc)" />
                                <FormControlLabel value="alphabetical" control={<Radio />} label="Alphabetically" />
                            </RadioGroup>
                        </FormControl>
                    </Box>

                    <Box sx={{display: "flex", gap: "3px", alignItems: "center"}}>
                        <Typography variant="h6" fontSize={14} fontWeight={400}>
                            Sort Parent Categories by Category Order
                        </Typography>
                        <Checkbox {...label} defaultChecked />
                    </Box>

                    <Button type="submit" variant="contained" size="small" sx={{ width: "fit-content", marginLeft: 'auto', paddingX: 4}}>
                        Save Changes
                    </Button>
                </Stack>
            </Paper>
        </Stack>
    )
}