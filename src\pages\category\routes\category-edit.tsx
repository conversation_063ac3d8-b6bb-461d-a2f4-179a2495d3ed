import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import { FormControl, InputLabel, Radio, RadioGroup, SelectChangeEvent } from '@mui/material';
import Paper from '@mui/material/Paper';
import useCategoryForm from 'pages/category/hooks/useCategoryForm.ts';
import useCategoryDetails from 'pages/category/hooks/useCategoryDetails.ts';
import CircularProgress from '@mui/material/CircularProgress';
import SelectMenu, { SelectedValue } from 'components/select-menu.tsx';
import FormControlLabel from '@mui/material/FormControlLabel';
import useCategories from 'pages/category/hooks/useCategories.ts';
import SingleFileInput from 'components/single-file-input.tsx';

export default function CategoryEdit() {
  const { categories } = useCategories({});
  const { categoryDetails, categoryDetailsLoading } = useCategoryDetails();
  const { formik } = useCategoryForm({ categoryDetails });

  if (categoryDetailsLoading || !categoryDetails) {
    return <CircularProgress />;
  }

  const handleChange = (event: SelectChangeEvent<string | number>, id: string) => {
    formik.setFieldValue(id, event.target.value);
  };

  const handleSelectChange = (id: string, value: SelectedValue | undefined) => {
    formik.setFieldValue(id, value);
  };

  return (
    <Paper elevation={3} sx={{ py: 4, width: '75%' }}>
      <Stack direction="row" alignItems="center" justifyContent="space-between" gap={2}>
        <Typography variant="h5" fontWeight={600}>
          Edit Category
        </Typography>
        <Button type="submit" variant="contained" size="small">
          View Categories
        </Button>
      </Stack>
      <Stack onSubmit={formik.handleSubmit} component="form" direction="column" gap={2} mt={4}>
        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="name"
          >
            Category Name (English)
          </InputLabel>
          <TextField
            id="name"
            type="text"
            variant="filled"
            placeholder="Category Name"
            autoComplete="name"
            fullWidth
            autoFocus
            required
            error={!!formik.errors.categoryName?.en && formik.touched.categoryName?.en}
            helperText={
              formik.errors.categoryName?.en && formik.touched.categoryName?.en
                ? formik.errors.categoryName?.en
                : ''
            }
            {...formik.getFieldProps('categoryName.en')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="categoryName.de"
          >
            Category Name (German)
          </InputLabel>
          <TextField
            id="categoryName.de"
            type="text"
            variant="filled"
            placeholder="Category Name"
            autoComplete="categoryName.de"
            fullWidth
            required
            {...formik.getFieldProps('categoryName.en')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="categoryName.de"
          >
            Category Name (German)
          </InputLabel>
          <TextField
            id="categoryName.de"
            type="text"
            variant="filled"
            placeholder="Category Name"
            autoComplete="categoryName.de"
            fullWidth
            required
            {...formik.getFieldProps('categoryName.de')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="categoryName.fr"
          >
            Category Name (French)
          </InputLabel>
          <TextField
            id="categoryName.fr"
            type="text"
            variant="filled"
            placeholder="Category Name"
            autoComplete="categoryName.fr"
            fullWidth
            required
            {...formik.getFieldProps('categoryName.fr')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="categoryName.it"
          >
            Category Name (Italian)
          </InputLabel>
          <TextField
            id="categoryName.it"
            type="text"
            variant="filled"
            placeholder="Category Name"
            autoComplete="categoryName.it"
            fullWidth
            required
            {...formik.getFieldProps('categoryName.it')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="categoryName.es"
          >
            Category Name (Spanish)
          </InputLabel>
          <TextField
            id="categoryName.es"
            type="text"
            variant="filled"
            placeholder="Category Name"
            autoComplete="categoryName.es"
            fullWidth
            required
            {...formik.getFieldProps('categoryName.es')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="metaTitle"
          >
            Title (Meta Tag)
          </InputLabel>
          <TextField
            id="metaTitle"
            type="text"
            variant="filled"
            placeholder="Category Title"
            autoComplete="metaTitle"
            fullWidth
            required
            error={!!formik.errors.metaTitle && formik.touched.metaTitle}
            helperText={
              formik.errors.metaTitle && formik.touched.metaTitle ? formik.errors.metaTitle : ''
            }
            {...formik.getFieldProps('metaTitle')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="metaDescription"
          >
            Description (Meta Tag)
          </InputLabel>
          <TextField
            id="metaDescription"
            type="text"
            variant="filled"
            placeholder="Category Description"
            autoComplete="description"
            fullWidth
            required
            error={!!formik.errors.metaDescription && formik.touched.metaDescription}
            helperText={
              formik.errors.metaDescription && formik.touched.metaDescription
                ? formik.errors.metaDescription
                : ''
            }
            {...formik.getFieldProps('metaDescription')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="order"
          >
            Order
          </InputLabel>
          <TextField
            id="category_order"
            type="number"
            variant="filled"
            placeholder="Category Order"
            autoComplete="category_order"
            fullWidth
            required
            error={!!formik.errors.category_order && formik.touched.category_order}
            helperText={
              formik.errors.category_order && formik.touched.category_order
                ? formik.errors.category_order
                : ''
            }
            {...formik.getFieldProps('category_order')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="parent_category"
          >
            Parent Category
          </InputLabel>
          {categories && (
            <SelectMenu
              id="category"
              value={
                formik.values.category._id
                  ? categories
                      .filter((item) => item._id === formik.values.category._id)
                      .map((item) => ({ _id: item._id, name: item.categoryName.en }))[0] || {
                      _id: '',
                      name: '',
                    }
                  : formik.values.category
              }
              handleChange={handleSelectChange}
              options={[
                { _id: null, name: 'None' },
                ...categories.map((item) => ({ _id: item._id, name: item.categoryName.en })),
              ]}
            />
          )}
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginTop: '24px', marginBottom: '20px' }}
            size="small"
            htmlFor="description"
          >
            Description
          </InputLabel>

          <TextField
            id="description"
            name="description"
            multiline
            rows={4}
            fullWidth
            value={formik.values.description}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            placeholder="Enter category description"
            variant="outlined"
            size="small"
          />
        </Box>

        <Stack direction="row" alignItems="center">
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px', width: '50%' }}
            size="small"
            htmlFor="Visibility"
          >
            Visibility
          </InputLabel>
          <FormControl>
            <RadioGroup
              row
              aria-labelledby="demo-row-radio-buttons-group-label"
              {...formik.getFieldProps('visibility')}
              onChange={(e) => handleChange(e, 'visibility')}
            >
              <FormControlLabel value={true} control={<Radio />} label="Show" />
              <FormControlLabel value={false} control={<Radio />} label="Hide" />
            </RadioGroup>
          </FormControl>
        </Stack>

        <Stack direction="row" alignItems="center">
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px', width: '50%' }}
            size="small"
            htmlFor="showInMainMenu"
          >
            Show on Main Menu
          </InputLabel>
          <FormControl>
            <RadioGroup
              row
              aria-labelledby="demo-row-radio-buttons-group-label"
              {...formik.getFieldProps('showInMainMenu')}
              onChange={(e) => handleChange(e, 'showInMainMenu')}
            >
              <FormControlLabel value={true} control={<Radio />} label="Yes" />
              <FormControlLabel value={false} control={<Radio />} label="No" />
            </RadioGroup>
          </FormControl>
        </Stack>

        <Stack direction="row" alignItems="center">
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px', width: '50%' }}
            size="small"
            htmlFor="show-image-on-main-menu"
          >
            Show on Main Menu
          </InputLabel>
          <FormControl>
            <RadioGroup
              row
              aria-labelledby="demo-row-radio-buttons-group-label"
              {...formik.getFieldProps('showImageOnMainMenu')}
              onChange={(e) => handleChange(e, 'showImageOnMainMenu')}
            >
              <FormControlLabel value={true} control={<Radio />} label="Yes" />
              <FormControlLabel value={false} control={<Radio />} label="No" />
            </RadioGroup>
          </FormControl>
        </Stack>

        {formik.isSubmitting ? (
          <CircularProgress />
        ) : (
          <SingleFileInput
            label="Upload image"
            getFile={(file) => formik.setFieldValue('image', file)}
            existingFile={categoryDetails.image}
          />
        )}

        <Button
          type="submit"
          variant="contained"
          size="medium"
          fullWidth
          disabled={!formik.isValid || formik.isSubmitting}
        >
          Submit
        </Button>
      </Stack>
    </Paper>
  );
}