import { postRequest } from 'vbrae-utils';

type BlogPostDto = {
  image: string;
  category: string;
  language: string;
  keywords: string[];
  details: {
    germanDetails: {
      image: string;
      description: string;
    };
    frenchDetails: {
      image: string;
      description: string;
    };
    description: string;
  };
  title: string;
  summary: string;
  tags: string[];
};


export async function postBlogForm(props: BlogPostDto): Promise<{message: string}> {
  const r = await postRequest<BlogPostDto>({
    url: 'blog',
    data: props,
    useAuth: true,
  });
  return r.response;
}