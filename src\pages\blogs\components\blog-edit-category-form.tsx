import useBlogCategoryDetails from "pages/blogs/hooks/useBlogCategoryDetails.ts";
import useBlogCategoryEdit from "pages/blogs/hooks/useBlogCategoryEdit.ts";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import {InputLabel} from "@mui/material";
import SelectMenu from "components/select-menu.tsx";
import {languages} from "pages/category/const/languages.ts";
import TextField from "@mui/material/TextField";
import Button from "@mui/material/Button";
import CircularProgress from "@mui/material/CircularProgress";

export default function BlogEditCategoryForm(){

    const { categoryDetails } = useBlogCategoryDetails();
    const {formik} = useBlogCategoryEdit({categoryDetails});

    if(!categoryDetails) return <CircularProgress />;

    return (
        <form onSubmit={formik.handleSubmit}>
            <Typography variant="h5" fontWeight={600} sx={{fontSize: {xs: '16px', sm: '18px'}}}>
                Edit Category
            </Typography>

            <Box width={{xs: '100%', sm: '200px'}}>
                <InputLabel
                    component="label"
                    sx={{fontSize: '12px', marginBottom: '20px'}}
                    size="small"
                    htmlFor="language"
                >
                    Language
                </InputLabel>
                <SelectMenu
                    id="language"
                    value={formik.values.language}
                    handleChange={(_, value) => formik.setFieldValue('language', value)}
                    options={languages.slice(1)}
                />
            </Box>

            <Box>
                <InputLabel
                    component="label"
                    sx={{fontSize: '12px', marginBottom: '20px'}}
                    size="small"
                    htmlFor="categoryName"
                >
                    Category Name
                </InputLabel>
                <TextField
                    id="categoryName"
                    type="text"
                    variant="filled"
                    placeholder="Category Name"
                    autoComplete="categoryName"
                    fullWidth
                    required
                    {...formik.getFieldProps('categoryName')}
                />
            </Box>

            <Box>
                <InputLabel
                    component="label"
                    sx={{fontSize: '12px', marginBottom: '20px'}}
                    size="small"
                    htmlFor="description"
                >
                    Description (Meta Tag)
                </InputLabel>
                <TextField
                    id="description"
                    type="text"
                    variant="filled"
                    placeholder="Description"
                    autoComplete="description"
                    fullWidth
                    required
                    {...formik.getFieldProps('description')}
                />
            </Box>

            <Box>
                <InputLabel
                    component="label"
                    sx={{fontSize: '12px', marginBottom: '20px'}}
                    size="small"
                    htmlFor="keywords"
                >
                    Keywords (Meta Tag)
                </InputLabel>
                <TextField
                    id="keywords"
                    type="text"
                    variant="filled"
                    placeholder="Meta Keywords"
                    autoComplete="keywords"
                    fullWidth
                    required
                    {...formik.getFieldProps('keywords')}
                />
            </Box>

            <Box>
                <InputLabel
                    component="label"
                    sx={{fontSize: '12px', marginBottom: '20px'}}
                    size="small"
                    htmlFor="order"
                >
                    Order
                </InputLabel>
                <TextField
                    id="order"
                    type="number"
                    variant="filled"
                    placeholder="Order"
                    autoComplete="order"
                    required
                    fullWidth
                    {...formik.getFieldProps('order')}
                />
            </Box>

            <Button
                type="submit"
                variant="contained"
                size="medium"
                disabled={formik.isSubmitting}
                fullWidth
                sx={{marginTop: '20px'}}
            >
                {formik.isSubmitting ? 'Processing...' : 'Edit Category'}
            </Button>
        </form>
    )
}