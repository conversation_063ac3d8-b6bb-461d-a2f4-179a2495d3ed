import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import Grid from '@mui/material/Grid';
import TopCards from 'components/sections/dashboard/top-cards';
import WebsiteVisitors from 'components/sections/dashboard/website-visitors';
import RevenueByCustomer from 'components/sections/dashboard/revenue-by-customer';
import Products from 'components/sections/dashboard/products';
import CompletedTask from 'components/sections/dashboard/completed-task';
import OrdersStatus from 'components/sections/dashboard/orders-status';
const Dashboard = () => {
    return (_jsxs(Grid, { container: true, spacing: { xs: 2.5, sm: 3, lg: 3.75 }, children: [_jsx(Grid, { item: true, xs: 12, children: _jsx(TopCards, {}) }), _jsx(Grid, { item: true, xs: 12, xl: 4, children: _jsx(WebsiteVisitors, {}) }), _jsx(Grid, { item: true, xs: 12, xl: 8, children: _jsx(RevenueByCustomer, {}) }), _jsx(Grid, { item: true, xs: 12, xl: 4, children: _jsx(Products, {}) }), _jsx(Grid, { item: true, xs: 12, xl: 8, children: _jsx(CompletedTask, {}) }), _jsx(Grid, { item: true, xs: 12, children: _jsx(OrdersStatus, {}) })] }));
};
export default Dashboard;
