import React, { useId, useState } from 'react';
import { Box, Button, CircularProgress, IconButton, InputLabel } from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';

interface FileInputWithPreviewProps {
    getFile: (files: File[], filePreviews: string[]) => void;
    label: string;
    existingFile?: string[];
}

const FileInputWithPreview = ({
                                  getFile,
                                  label,
                                  existingFile = [],
                              }: FileInputWithPreviewProps) => {
    const [filePreviews, setFilePreviews] = useState<string[]>( existingFile );
    const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const id = useId();

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        setIsLoading(true);
        if (files && files.length > 0) {
            const validFiles: File[] = [];
            const previews: string[] = [...filePreviews];

            Array.from(files).forEach((file) => {
                if (file.type.startsWith('image/')) {
                    validFiles.push(file);

                    const reader = new FileReader();
                    reader.onloadend = () => {
                        previews.push(reader.result as string);
                        setFilePreviews([...previews]);
                    };
                    reader.readAsDataURL(file);
                } else {
                    alert(`${file.name} is not a valid image file (JPG, PNG, JPEG).`);
                }
            });

            if (validFiles.length > 0) {
                setSelectedFiles([...selectedFiles, ...validFiles]);
                getFile([...selectedFiles, ...validFiles], filePreviews);
            }
            setIsLoading(false)
        } else {
            alert('No files selected or invalid file type.');
        }
    };

    const handleRemoveFile = (index: number) => {
        const updatedPreviews = filePreviews.filter((_, i) => i !== index);
        const updatedFiles = selectedFiles.filter((_, i) => i !== index);

        setFilePreviews(updatedPreviews);
        setSelectedFiles(updatedFiles);
        getFile(updatedFiles, updatedPreviews);
    };

    return (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <InputLabel
                component="label"
                sx={{ fontSize: '12px', marginBottom: '20px', width: '50%' }}
            >
                {label}
            </InputLabel>

            <input
                type="file"
                accept="image/*"
                style={{ display: 'none' }}
                multiple={true}
                onChange={handleFileChange}
                id={`${id}-file-input`}
            />

            <label htmlFor={`${id}-file-input`}>
                <IconButton
                    component="span"
                    sx={{
                        backgroundColor: 'primary.main',
                        color: 'white',
                        padding: '12px',
                        '&:hover': {
                            backgroundColor: 'primary.dark',
                        },
                    }}
                >
                    <CloudUploadIcon sx={{ fontSize: 40 }} />
                </IconButton>
            </label>

            {isLoading && <CircularProgress sx={{ marginTop: 2 }} />}

            {filePreviews.length > 0 && (
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        flexWrap: 'wrap',
                        gap: 2,
                        mt: 2,
                    }}
                >
                    {filePreviews.map((preview, index) => (
                        <Box key={index} sx={{ position: 'relative' }}>
                            <img
                                src={preview}
                                alt={`Preview ${index}`}
                                style={{
                                    width: '150px',
                                    height: '150px',
                                    objectFit: 'contain',
                                    borderRadius: '8px',
                                }}
                            />
                            <Button
                                variant="contained"
                                color="secondary"
                                onClick={() => handleRemoveFile(index)}
                                sx={{
                                    position: 'absolute',
                                    top: 4,
                                    right: 4,
                                    minWidth: '32px',
                                    minHeight: '32px',
                                    padding: 0,
                                }}
                            >
                                X
                            </Button>
                        </Box>
                    ))}
                </Box>
            )}
        </Box>
    );
};

export default FileInputWithPreview;
