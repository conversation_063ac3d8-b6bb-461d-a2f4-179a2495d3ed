import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import getFaqDetails from 'pages/faq/api/getFaqDetails.ts';

export type FaqPropsDto = {
  id: string;
};

export default function useFaqDetails(props:FaqPropsDto) {
    const { data: details, loading: detailsLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['faq-details', {...props}],
            queryFn: () => getFaqDetails(props),
            onError: showError,
        }),
        transform: (data) => data.data,
    });
    return { details, detailsLoading };
}
