import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import { FormHelperText, InputLabel } from '@mui/material';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import useGiveawayForm from 'pages/give-away/hooks/useGiveawayForm.ts';
import Paper from '@mui/material/Paper';
import { FormikErrors, FormikTouched } from 'formik';
import { GiveawayAction } from 'pages/give-away/types/give-away.ts';

export default function GiveawayAdd() {

  const { formik } = useGiveawayForm({ existingGiveaway: undefined });

  return (
    <form onSubmit={formik.handleSubmit}>
      <Typography variant="h5" fontWeight={600} sx={{ fontSize: { xs: '16px', sm: '18px' }, mb: 2 }}>
        Add Giveaway
      </Typography>

      {/* Title */}
      <Box mb={2}>
        <InputLabel htmlFor="title">Title</InputLabel>
        <TextField
          fullWidth
          variant="filled"
          id="title"
          {...formik.getFieldProps('title')}
          error={formik.touched.title && Boolean(formik.errors.title)}
        />
        {formik.touched.title && formik.errors.title && (
          <FormHelperText error>{formik.errors.title}</FormHelperText>
        )}
      </Box>

      {/* Description */}
      <Box mb={2}>
        <InputLabel htmlFor="description">Description</InputLabel>
        <TextField
          fullWidth
          variant="filled"
          id="description"
          {...formik.getFieldProps('description')}
          error={formik.touched.description && Boolean(formik.errors.description)}
        />
        {formik.touched.description && formik.errors.description && (
          <FormHelperText error>{formik.errors.description}</FormHelperText>
        )}
      </Box>

      {/* Start Date */}
      <Box mb={2}>
        <InputLabel htmlFor="startDate">Start Date</InputLabel>
        <TextField
          fullWidth
          variant="filled"
          type="date"
          id="startDate"
          inputProps={{
            min: new Date().toISOString().split('T')[0],
          }}
          {...formik.getFieldProps('startDate')}
          error={formik.touched.startDate && Boolean(formik.errors.startDate)}
        />
        {formik.touched.startDate && formik.errors.startDate && (
          <FormHelperText error>{formik.errors.startDate}</FormHelperText>
        )}
      </Box>

      {/* End Date */}
      <Box mb={2}>
        <InputLabel htmlFor="endDate">End Date</InputLabel>
        <TextField
          fullWidth
          variant="filled"
          type="date"
          id="endDate"
          inputProps={{
            min: new Date().toISOString().split('T')[0],
          }}
          {...formik.getFieldProps('endDate')}
          error={formik.touched.endDate && Boolean(formik.errors.endDate)}
        />
        {formik.touched.endDate && formik.errors.endDate && (
          <FormHelperText error>{formik.errors.endDate}</FormHelperText>
        )}
      </Box>


      {/* Prizes */}
      <Typography variant="subtitle1" mt={3}>
        Prizes
      </Typography>
      {formik.values.prizes.map((prize, index) => {
        const touchedPrizes = formik.touched.prizes as string[] | undefined;
        const errorPrizes = formik.errors.prizes as (string | undefined)[];
        const hasError = touchedPrizes?.[index] && errorPrizes?.[index];
        return (
          <Box key={index} mb={2}>
            <Box display="flex" alignItems="center">
              <TextField
                fullWidth
                variant="filled"
                placeholder={`Prize #${index + 1}`}
                value={prize}
                onChange={(e) =>
                  formik.setFieldValue(
                    'prizes',
                    formik.values.prizes.map((p, i) => (i === index ? e.target.value : p))
                  )
                }
                error={Boolean(hasError)}
                onBlur={() => formik.setFieldTouched(`prizes.${index}`, true)}
              />
              <IconButton
                onClick={() =>
                  formik.setFieldValue(
                    'prizes',
                    formik.values.prizes.filter((_, i) => i !== index)
                  )
                }
                disabled={formik.values.prizes.length === 1}
              >
                <DeleteIcon />
              </IconButton>
            </Box>
            {hasError && (
              <FormHelperText error>{formik.errors.prizes?.[index]}</FormHelperText>
            )}
          </Box>
        );
      })}

      <Button
        startIcon={<AddIcon />}
        onClick={() => formik.setFieldValue('prizes', [...formik.values.prizes, ''])}
        sx={{ mb: 3 }}
      >
        Add Prize
      </Button>

      {/* Actions */}
      <Typography variant="subtitle1">Actions</Typography>
      {formik.values.actions.map((action, index) => {
        const actionTouched = formik.touched.actions?.[index] as FormikTouched<GiveawayAction> | undefined;
        const actionErrors = formik.errors.actions?.[index] as FormikErrors<GiveawayAction> | undefined;

        return (
          <Paper key={index} sx={{ p: 2, mb: 2, borderRadius: 2 }}>
            <Box mb={2}>
              {/* Action Type */}
              <Box mb={2}>
                <InputLabel sx={{ fontSize: '12px', mb: 1 }}>
                  Action Type #{index + 1}
                </InputLabel>
                <TextField
                  fullWidth
                  variant="filled"
                  placeholder="Enter action type"
                  value={action.type}
                  onChange={(e) =>
                    formik.setFieldValue(`actions[${index}].type`, e.target.value)
                  }
                  onBlur={() => formik.setFieldTouched(`actions[${index}].type`, true)}
                  error={Boolean(actionTouched?.type && actionErrors?.type)}
                  helperText={actionTouched?.type && actionErrors?.type}
                />
              </Box>

              {/* Label */}
              <Box mb={2}>
                <InputLabel sx={{ fontSize: '12px', mb: 1 }}>Label</InputLabel>
                <TextField
                  fullWidth
                  variant="filled"
                  placeholder="Enter label"
                  value={action.label}
                  onChange={(e) =>
                    formik.setFieldValue(`actions[${index}].label`, e.target.value)
                  }
                  onBlur={() => formik.setFieldTouched(`actions[${index}].label`, true)}
                  error={Boolean(actionTouched?.label && actionErrors?.label)}
                  helperText={actionTouched?.label && actionErrors?.label}
                />
              </Box>

              {/* Points */}
              <Box mb={2}>
                <InputLabel sx={{ fontSize: '12px', mb: 1 }}>Points</InputLabel>
                <TextField
                  fullWidth
                  type="number"
                  variant="filled"
                  placeholder="Enter points"
                  value={action.points}
                  onChange={(e) =>
                    formik.setFieldValue(`actions[${index}].points`, Number(e.target.value))
                  }
                  onBlur={() => formik.setFieldTouched(`actions[${index}].points`, true)}
                  error={Boolean(actionTouched?.points && actionErrors?.points)}
                  helperText={actionTouched?.points && actionErrors?.points}
                />
              </Box>

              {/* URL */}
              <Box mb={2}>
                <InputLabel sx={{ fontSize: '12px', mb: 1 }}>URL</InputLabel>
                <TextField
                  fullWidth
                  variant="filled"
                  placeholder="Enter URL"
                  value={action.url}
                  onChange={(e) =>
                    formik.setFieldValue(`actions[${index}].url`, e.target.value)
                  }
                  onBlur={() => formik.setFieldTouched(`actions[${index}].url`, true)}
                  error={Boolean(actionTouched?.url && actionErrors?.url)}
                  helperText={actionTouched?.url && actionErrors?.url}
                />
              </Box>

              <IconButton
                onClick={() =>
                  formik.setFieldValue(
                    'actions',
                    formik.values.actions.filter((_, i) => i !== index)
                  )
                }
                disabled={formik.values.actions.length === 1}
              >
                <DeleteIcon />
              </IconButton>
            </Box>
          </Paper>
        );
      })}


      <Button
        startIcon={<AddIcon />}
        onClick={() =>
          formik.setFieldValue('actions', [
            ...formik.values.actions,
            { type: '', label: '', points: 1, url: '' },
          ])
        }
      >
        Add Action
      </Button>

      {/* Submit */}
      <Button
        type="submit"
        variant="contained"
        size="medium"
        disabled={formik.isSubmitting}
        fullWidth
        sx={{ mt: 4 }}
      >
        {formik.isSubmitting ? 'Processing...' : 'Add Giveaway'}
      </Button>
    </form>
  );
}
