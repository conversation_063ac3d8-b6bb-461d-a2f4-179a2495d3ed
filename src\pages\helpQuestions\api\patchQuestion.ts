import { patchRequest } from 'vbrae-utils';

type Props = {
  title: string;
  content: string;
  isActive: boolean;
  categoryId: string;
  subcategoryId: string;
  questionId: string;
};

export async function patchQuestion(props: Props): Promise<{message: string}> {

    const r = await patchRequest<Props>({
        url: `help/${props.categoryId}/subcategories/${props.subcategoryId}/questions/${props.questionId}`,
        data: props,
        useAuth: true,
    });
    return r.response;
}