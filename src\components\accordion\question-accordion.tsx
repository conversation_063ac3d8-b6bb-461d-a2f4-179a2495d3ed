import {
  AccordionCategoryDto,
  AccordionQuestionDto,
  AccordionSubcategoryDto,
} from 'pages/category/types/accordion.ts';
import Typography from '@mui/material/Typography';
import Chip from '@mui/material/Chip';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import IconifyIcon from 'components/base/IconifyIcon.tsx';
import AccordionSummary from '@mui/material/AccordionSummary';
import Accordion from '@mui/material/Accordion';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Stack from '@mui/material/Stack';

interface CustomAccordionProps {
  data: AccordionCategoryDto[];
  onParentDelete: (item: { _id: string, parentId?: string }) => void;
  onChildDelete: (item: { _id: string, parentId?: string }) => void;
  onParentEdit: (item: { _id: string, parentId?: string }) => void;
  onChildEdit: (item: { _id: string, parentId?: string }) => void;
  onAdd: (item: { _id: string }) => void;
}

const QuestionAccordion: React.FC<CustomAccordionProps> = ({
                                                               data,
                                                               onChildEdit,
                                                               onParentEdit,
                                                               onAdd,
                                                           }) => {

    const renderAccordion = (item: any, level: number = 0, parentId:string = "") => {
        const isCategory = level === 0;
        const isSubcategory = level === 1;
        const isQuestion = level === 2;

        const editFunction = isQuestion ? onChildEdit : onParentEdit;

        return (
            <Accordion key={item._id} defaultExpanded={false}>
                <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls={`${item._id}-title`}
                    id={`${item._id}-header`}
                    sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        padding: 0,
                        alignItems: { xs: "start" },
                        flexDirection: { xs: "column", sm: "row" },
                        "& .MuiAccordionSummary-content": {
                            width: { xs: '100%', sm: 'auto' },
                        },
                    }}
                >
                    <Typography
                        component="span"
                        mr={2}
                        sx={{ fontSize: { xs: "12px", sm: "14px" }, fontWeight: "bold" }}
                    >
                        {isCategory && "📂 Category:"}
                        {isSubcategory && "📁 Subcategory:"}
                        {isQuestion && "❓ Question:"}
                    </Typography>

                    <Typography
                        component="span"
                        sx={{ fontSize: { xs: "12px", sm: "14px" } }}
                    >
                        {item.title}
                    </Typography>

                    <Box sx={{ marginLeft: 'auto' }}>
                        {/* Status Chip */}
                        {!isQuestion && (
                            <>
                                <Chip
                                    label={item.isActive ? "Active" : "Disabled"}
                                    color={item.isActive ? "success" : "error"}
                                    sx={{
                                        margin: '2px',
                                        borderRadius: '16px',
                                        padding: { sm: "6px 12px", xs: "2px 6px" },
                                        color: '#333',
                                        fontSize: { xs: '12px', sm: '14px' }
                                    }}
                                />
                            </>
                        )}

                        {isCategory && <Button
                            sx={{ padding: 0, minWidth: { xs: 24, lg: 64 } }}
                            onClick={(event) => {
                                event.stopPropagation();
                                onAdd({ _id: item._id });
                            }}
                        >
                            <IconifyIcon icon={'ion:add-outline'} sx={{ fontSize: '20px' }} />
                        </Button>}

                        {/* Edit Button */}
                        {isSubcategory && <>
                            <Button
                                sx={{ padding: 0, minWidth: { xs: 24, lg: 64 } }}
                                onClick={(event) => {
                                    event.stopPropagation();
                                    editFunction({ _id: item._id, parentId });
                                }}
                            >
                                <IconifyIcon icon={'ion:create-outline'} sx={{ fontSize: '20px' }} />
                            </Button>
                        </>}
                    </Box>
                </AccordionSummary>

                {/* Subcategories */}
                {isCategory && item.subcategories?.length > 0 && item.subcategories.map((sub: AccordionSubcategoryDto) => (
                    renderAccordion(sub as AccordionSubcategoryDto, 1, item._id)
                ))}

                {/* Questions inside Subcategory */}
                {isSubcategory && item.questions?.length > 0 && item.questions.map((question : AccordionQuestionDto) => (
                    renderAccordion(question as AccordionQuestionDto, 2, item._id)
                ))}

            </Accordion>
        );
    };

    return (
        <Stack direction="column" spacing={3} my={5}>
            {data.map((item) => renderAccordion(item))}
        </Stack>
    );
};

export default QuestionAccordion;
