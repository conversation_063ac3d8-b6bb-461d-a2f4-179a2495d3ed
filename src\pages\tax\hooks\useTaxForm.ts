import { useFormik } from 'formik';
import { useEffect, useState } from 'react';
import { useMutation, useQueryClient } from 'react-query';
import { showError } from 'vbrae-utils';
import { patchTaxes } from 'pages/tax/api/patchTaxes.ts';
import { ErrorType } from 'pages/authentication/hooks/useLoginForm';
import * as Yup from 'yup';
import { EUTax } from '../types/tax';

type QuestionFormPropsDto = {
  existingTax: EUTax;
  handleClose: () => void;
};

const taxSchema = Yup.object().shape({
  taxPercentage: Yup.string(),
});

export default function useTaxForm({ existingTax, handleClose }: QuestionFormPropsDto) {

  const [initialValues, setInitialState] = useState({
    taxPercentage: '0',
  });

  useEffect(() => {
    if (!existingTax._id) return;

    setInitialState({
      taxPercentage: existingTax.taxPercentage,
    });
  }, [existingTax]);

  const { mutateAsync } = useMutation(patchTaxes, {
    onError: (error: ErrorType) => showError(error),
  });

  const queryClient = useQueryClient();

  const formik = useFormik({
    initialValues,
    enableReinitialize: true,
    validationSchema: taxSchema,
    onSubmit: async (values, { setSubmitting }) => {
      setSubmitting(true);
      const response = await mutateAsync({ ...values, _id: existingTax._id  });
      setSubmitting(false);
      if (response) {
        queryClient.invalidateQueries('tax').finally();
        handleClose();
      }
    },
  });

  return { formik };
}
