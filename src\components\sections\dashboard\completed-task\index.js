import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import RateChip from 'components/chips/RateChip';
import DateSelect from 'components/dates/DateSelect';
import IconifyIcon from 'components/base/IconifyIcon';
import CompletedTaskChart from './CompletedTaskChart';
const CompletedTask = () => {
    return (_jsxs(Paper, { sx: { height: 300 }, children: [_jsxs(Stack, { alignItems: "center", spacing: 0.6, children: [_jsx(IconifyIcon, { icon: "ph:clock-fill", color: "text.secondary", fontSize: "h6.fontSize" }), _jsx(Typography, { variant: "body2", color: "text.secondary", children: "Completed tasks over time" })] }), _jsxs(Stack, { mt: 1.5, alignItems: "center", justifyContent: "space-between", children: [_jsxs(Stack, { alignItems: "center", gap: 0.875, children: [_jsx(Typography, { variant: "h3", fontWeight: 600, letterSpacing: 1, children: "257" }), _jsx(RateChip, { rate: '16.8%', isUp: true })] }), _jsx(DateSelect, {})] }), _jsx(Box, { height: 220, children: _jsx(CompletedTaskChart, { sx: { height: '100% !important' } }) })] }));
};
export default CompletedTask;
