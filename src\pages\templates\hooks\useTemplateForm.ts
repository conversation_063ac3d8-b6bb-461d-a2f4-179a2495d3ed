import { useMutation } from 'react-query';
import * as Yup from 'yup';
import { useFormik } from 'formik';
import { showError, showSuccess } from 'vbrae-utils';
import { useEffect, useState } from 'react';
import { SelectedValue } from 'components/select-menu.tsx';
import { postTemplate } from 'pages/templates/api/postTemplate.ts';
import { TemplateDto } from 'pages/templates/types/template.ts';
import { uploadUrl } from 'pages/category/api/uploadUrl.ts';
import { updateTemplate } from 'pages/templates/api/updateTemplate.ts';
import { productTypes } from 'pages/templates/const/productType.ts';

interface ErrorType {
  message: string;
}

interface TemplateDetailsProps {
    templateDetails?: TemplateDto;
}


const templateSchema = Yup.object().shape({
    coverImage: Yup.mixed().required("Cover Image is required"),
    templateName: Yup.string()
        .trim()
        .min(3, 'Minimum 3 characters')
        .max(50, 'Maximum 50 characters')
        .required('Template Name is required'),
    slug: Yup.string().trim(),
    price: Yup.number()
        .required('Price is required'),
    listingType: Yup.object().shape({
        _id: Yup.string().nullable(),
        name: Yup.string(),
    }),
    category:  Yup.object().shape({
        _id: Yup.string().min(4, 'Category ID is required').required("Category is required"),
        name: Yup.string(),
    }),
    subcategory:  Yup.object().shape({
        _id: Yup.mixed().nullable(),
        name: Yup.string(),
    }),
    region:Yup.object().shape({
        _id: Yup.string().nullable(),
        name: Yup.string(),
    }),
    genres: Yup.string(),
    releaseDate: Yup.date(),
    preOrder: Yup.object().shape({
        _id: Yup.string().nullable(),
        name: Yup.string(),
    }),
    dlc: Yup.string(),
    specificCountrySellingOption: Yup.object().shape({
        _id: Yup.string().nullable(),
        name: Yup.string(),
    }),
    languages: Yup.array().required('Languages are required'),
    videos: Yup.string().url('Must be a valid URL'),
    images: Yup.mixed().nullable(),
    serviceFee: Yup.string().nullable(),
    details: Yup.object().shape({
        title: Yup.string(),
        description: Yup.string(),
        seo: Yup.object().shape({
            metaTitle: Yup.string(),
            metaDescription: Yup.string(),
            metaKeywords: Yup.string()
        }),
    }),
});

export interface InitialValuesDto {
    coverImage: null | string | File,
    templateName: string;
    slug: string;
    price: number;
    listingType: SelectedValue;
    category: SelectedValue;
    subcategory: SelectedValue;
    region: SelectedValue;
    genres: string;
    releaseDate: string;
    preOrder: SelectedValue;
    dlc: boolean;
    specificCountrySellingOption: SelectedValue;
    languages: string[];
    videos: string;
    images: File[] | string[];
    serviceFee: string | null;
    details: {
        title: string;
        description: string;
        seo: {
            metaTitle: string;
            metaDescription: string;
            metaKeywords: string;
        };
    };
};


export default function useTemplateForm({templateDetails} : TemplateDetailsProps) {
    const [initialValues, setInitialValues] = useState<InitialValuesDto>({
        coverImage: null,
        templateName: '',
        price: 1,
        slug: '',
        listingType: productTypes[0],
        category: {
            _id: null, name: "--Choose--"
        },
        subcategory: {
            _id: null, name: "None"
        },
        region: {
            _id: null, name: "--Choose--"
        },
        genres: '',
        releaseDate: '',
        preOrder: {
            _id: null, name: "--Choose--"
        },
        dlc: false,
        specificCountrySellingOption: {
            _id: null, name: "--Choose--"
        },
        languages: [],
        videos: '',
        images: [],
        serviceFee: null,
        details: {
            title: '',
            description: '',
            seo: {
                metaTitle: '',
                metaDescription: '',
                metaKeywords: '',
            },
        },
    });

    useEffect(() => {
        if (!templateDetails) return;

        setInitialValues({
            price: templateDetails.price,
            coverImage: templateDetails.coverImage[0],
            templateName: templateDetails.templateName,
            slug: templateDetails.slug,
            listingType: templateDetails.listingType === "account" ? productTypes[0] : productTypes[1],
            category: {_id: templateDetails.category?._id ?? "", name: templateDetails.category?.categoryName.en ?? "None"},
            subcategory: {_id: templateDetails.subcategory?._id ?? "", name: templateDetails.subcategory?.categoryName.en ?? "None"},
            region: {_id: templateDetails.region, name: templateDetails.region ?? "--Choose--"},
            genres: templateDetails.genres,
            releaseDate: templateDetails.releaseDate?.split('T')[0],
            preOrder: {_id: templateDetails.preOrder, name: ''},
            dlc: templateDetails.dlc,
            specificCountrySellingOption: {_id: templateDetails.specificCountrySellingOption, name: templateDetails.specificCountrySellingOption ? '' : '--Choose--'},
            languages: templateDetails.languages,
            videos: templateDetails.videos?.length > 0 ? templateDetails.videos[0] : '',
            images: templateDetails.images,
            serviceFee: templateDetails.serviceFee,
            details: {
                title: templateDetails.details?.title,
                description: templateDetails.details?.description,
                seo: {
                    metaTitle: templateDetails.details?.seo?.metaTitle,
                    metaDescription: templateDetails.details?.seo?.metaDescription,
                    metaKeywords: templateDetails.details?.seo?.metaKeywords,
                },
            },
        });
    }, [templateDetails]);

    const { mutateAsync : updateAsync } = useMutation(updateTemplate, {
        onError: (error: ErrorType )=>showError(error),
    });

    const { mutateAsync } = useMutation(postTemplate, {
        onError: (error: ErrorType )=>showError(error),
    });

    const formik = useFormik({
      initialValues,
      enableReinitialize: true,
      validationSchema: templateSchema,
      onSubmit: async (values, { setSubmitting, resetForm }) => {
        setSubmitting(true);
        let response;

          const isString = (value: unknown): value is string => typeof value === 'string';
          const imageUrls: string[] = values.images.filter(isString);
          if (Array.isArray(values.images) && values.images.length > 0) {
              for (const image of values.images) {
                  if (image instanceof File) {
                      const fileType = image.name.split('.').pop()?.toLowerCase() || 'unknown';
                      const resignedResponse = await uploadUrl({ name: image.name, fileType });
                      const { url: resignedUrl, path: filePath } = resignedResponse;

                      await fetch(resignedUrl, {
                          method: 'PUT',
                          headers: {
                              'Content-Type': image.type,
                              'x-amz-acl': 'public-read'
                          },
                          body: image,
                      });

                      imageUrls.push(filePath);
                  }
              }
          }

          let coverImage = undefined;
          if(values.coverImage instanceof File) {
              const fileType = values.coverImage.name.split('.').pop()?.toLowerCase() || "unknown";
              const resignedResponse = await uploadUrl({ name: values.coverImage.name, fileType });
              const { url: resignedUrl, path: filePath } = resignedResponse;

              await fetch(resignedUrl, {
                  method: "PUT",
                  headers: { "Content-Type": values.coverImage.type, "x-amz-acl": "public-read" },
                  body: values.coverImage,
              });

              coverImage = filePath
          }

        if (!templateDetails) response = await mutateAsync({
                ...values,
                listingType: values.listingType._id as string,
                category: values.category._id as string,
                subcategory: values.subcategory._id as string,
                region: values.region._id as string,
                preOrder: Boolean(values.preOrder._id),
                specificCountrySellingOption: Boolean(values.specificCountrySellingOption),
                coverImage: coverImage,
                images: imageUrls,
                videos: values.videos ? [values.videos] : [],
                genres: values.genres,
        });
        if (templateDetails) response = await updateAsync({
          ...values,
            _id: templateDetails._id,
            listingType: values.listingType._id as string,
            category: values.category._id as string,
            subcategory: values.subcategory._id as string,
            region: values.region._id as string,
            preOrder: Boolean(values.preOrder._id),
            specificCountrySellingOption: Boolean(values.specificCountrySellingOption),
            coverImage: coverImage,
            images: imageUrls,
            videos: values.videos ? [values.videos] : [],
            genres: values.genres,
        });
        setSubmitting(false);
        resetForm();
        if (response) {
          showSuccess(response.message);
        }
      },
    });

    return { formik };
}