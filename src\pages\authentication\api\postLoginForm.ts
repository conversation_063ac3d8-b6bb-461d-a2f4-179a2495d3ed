import {postRequest} from "vbrae-utils";
import {UserDto} from "pages/authentication/types/user.ts";

interface LoginProps {
    email: string;
    password: string;
}

interface LoginResponse {
    token: string;
    user: UserDto
}

export async function postLoginForm(props: LoginProps): Promise<LoginResponse | undefined> {
    const r = await postRequest<LoginProps>({
        url: 'users/login',
        data : props,
        useAuth: false
    });
    return r.response
}